package ai.friday.billpayment.adapters.api

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.adapters.dynamodb.UtilityAccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.UtilityAccountDynamoDAO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.utilityaccount.UtilityAccount
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountError
import ai.friday.billpayment.app.utilityaccount.UtilityAccountId
import ai.friday.billpayment.app.utilityaccount.UtilityAccountService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.fixture.UtilityAccountFixture
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

class BackofficeAccountUtilityControllerTest {

    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val repository = UtilityAccountDbRepository(client = UtilityAccountDynamoDAO(cli = dynamoDbEnhancedClient))
    private val notificationAdapter = mockk<NotificationAdapter>(relaxUnitFun = true)
    private val sqsMessagePublisher = mockk<MessagePublisher>(relaxUnitFun = true)
    val wallet = WalletFixture().buildWallet()
    val walletService = mockk<WalletService> {
        every { findWalletOrNull(any()) } returns wallet
    }
    private val service = UtilityAccountService(
        utilityAccountRepository = repository,
        accountRepository = mockk(relaxed = true),
        sqsMessagePublisher = sqsMessagePublisher,
        notificationAdapter = notificationAdapter,
        intercomAdapter = mockk(relaxed = true),
        kmsAdapter = mockk(relaxed = true),
        utilityAccountActivationQueueName = "",
        emailDomain = "",
        emailSenderService = mockk(relaxed = true),
        sender = "",
        manualWorkFlowEmail = "",
        utilityAccountMonitorService = mockk(relaxed = true),
        externalFilesRepositorySA = mockk(relaxed = true),
        utilityFlowRequestInvoicesQueueName = "",
        createBillService = mockk(relaxed = true),
        fichaCompensacaoService = mockk(relaxed = true),
        userJourneyService = mockk(relaxed = true),
        walletService = walletService,
        manualEntryService = mockk(relaxed = true),
        userEventService = mockk(relaxed = true),
    )
    private val controller = BackofficeAccountUtilityController(service, mockk(), notificationAdapter)

    @Nested
    @DisplayName("setStatus")
    inner class UtilityAccountSetStatus {

        @Test
        fun `quando a account utility não existe não deve atualizar`() {
            val response = controller.utilityAccountSetStatus(
                walletId = "new",
                utilityAccountId = "account",
                body = UtilityAccountSetStatusTO(
                    currentStatus = "CONNECTED",
                    newStatus = "DISCONNECTED",
                ),
            )

            response.status() shouldBe HttpStatus.NOT_FOUND
        }

        @Test
        fun `quando a account utility current informado for incorreto não deve atualizar`() {
            repository.save(
                UtilityAccountFixture.create(
                    id = UtilityAccountId("account"),
                    accountId = AccountId("any"),
                    walletId = WalletId("any"),
                    status = UtilityAccountConnectionStatus.CONNECTED,
                ),
            )

            val response = controller.utilityAccountSetStatus(
                walletId = "any",
                utilityAccountId = "account",
                body = UtilityAccountSetStatusTO(
                    currentStatus = "PENDING",
                    newStatus = "DISCONNECTED",
                ),
            )

            response.status() shouldBe HttpStatus.CONFLICT
        }

        @ParameterizedTest
        @EnumSource(
            UtilityAccountConnectionStatus::class,
            mode = EnumSource.Mode.EXCLUDE,
            names = ["CONNECTED", "DISCONNECTED", "INVALID_CREDENTIALS"],
        )
        fun `quando modificar para um estado que nao é final deve atualizar o status e não notificar`(status: UtilityAccountConnectionStatus) {
            repository.save(
                UtilityAccountFixture.create(
                    id = UtilityAccountId("any"),
                    accountId = AccountId("account"),
                    walletId = WalletId("wallet"),
                    status = UtilityAccountConnectionStatus.CONNECTED,
                ),
            )
            val response = controller.utilityAccountSetStatus(
                walletId = "wallet",
                utilityAccountId = "any",
                UtilityAccountSetStatusTO(
                    currentStatus = "CONNECTED",
                    newStatus = status.name,
                ),
            )

            response.status() shouldBe HttpStatus.NO_CONTENT

            verify(exactly = 0) { notificationAdapter.notifyUtilityAccountUpdatedStatus(any()) }

            repository.findById(
                UtilityAccountId("any"),
                WalletId("wallet"),
            )!!.status shouldBe status
        }

        @ParameterizedTest
        @EnumSource(
            UtilityAccountConnectionStatus::class,
            mode = EnumSource.Mode.INCLUDE,
            names = ["DISCONNECTED", "INVALID_CREDENTIALS"],
        )
        fun `quando modificar para um estado de erro final deve atualizar e  notificar`(status: UtilityAccountConnectionStatus) {
            repository.save(
                UtilityAccountFixture.create(
                    id = UtilityAccountId("any"),
                    accountId = wallet.founder.accountId,
                    walletId = wallet.id,
                    status = UtilityAccountConnectionStatus.PENDING,
                ),
            )
            val response = controller.utilityAccountSetStatus(
                walletId = wallet.id.value,
                utilityAccountId = "any",
                UtilityAccountSetStatusTO(
                    currentStatus = "PENDING",
                    newStatus = status.name,
                ),
            )

            response.status() shouldBe HttpStatus.NO_CONTENT
            val slot = slot<UtilityAccount>()
            verify(exactly = 1) { notificationAdapter.notifyUtilityAccountUpdatedStatus(capture(slot)) }
            slot.captured.status shouldBe status

            repository.findById(
                UtilityAccountId("any"),
                wallet.id,
            )!!.status shouldBe status
        }

        @Test
        fun `quando modificar para conectado deve atualizar e  notificar`() {
            repository.save(
                UtilityAccountFixture.create(
                    id = UtilityAccountId("any"),
                    accountId = wallet.founder.accountId,
                    walletId = wallet.id,
                    status = UtilityAccountConnectionStatus.PENDING,
                ),
            )
            val response = controller.utilityAccountSetStatus(
                walletId = wallet.id.value,
                utilityAccountId = "any",
                UtilityAccountSetStatusTO(
                    currentStatus = "PENDING",
                    newStatus = "CONNECTED",
                ),
            )

            response.status() shouldBe HttpStatus.NO_CONTENT
            val slot = slot<UtilityAccount>()
            verify(exactly = 1) { notificationAdapter.notifyUtilityAccountConnected(capture(slot), any()) }
            slot.captured.status shouldBe UtilityAccountConnectionStatus.CONNECTED

            repository.findById(
                UtilityAccountId("any"),
                wallet.id,
            )!!.status shouldBe UtilityAccountConnectionStatus.CONNECTED
        }
    }

    @Test
    fun `deve notificar o usuário quando for feita uma desconexão de conta legada`() {
        repository.save(
            UtilityAccountFixture.create(
                id = UtilityAccountId("any"),
                accountId = wallet.founder.accountId,
                walletId = wallet.id,
                status = UtilityAccountConnectionStatus.CONNECTED,
            ),
        )
        val response = controller.disconnectLegacy(
            walletId = wallet.id.value,
            utilityAccountId = "any",
        )

        response.status() shouldBe HttpStatus.NO_CONTENT

        val slot = slot<UtilityAccount>()
        verify(exactly = 1) { notificationAdapter.notifyDisconnectLegacyUtilityAccount(capture(slot)) }
        slot.captured.status shouldBe UtilityAccountConnectionStatus.DISCONNECTED

        repository.findById(
            UtilityAccountId("any"),
            wallet.id,
        )!!.status shouldBe UtilityAccountConnectionStatus.DISCONNECTED
    }

    @Nested
    @DisplayName("retry")
    inner class UtilityAccountRetry {
        @Test
        fun `deve retornar BAD_REQUEST quando a account utility não existe`() {
            val response = controller.utilityAccountRetry(
                walletId = "new",
                utilityAccountId = "account",
            )

            response.status() shouldBe HttpStatus.BAD_REQUEST
            response.body().toString() shouldBe UtilityAccountError.AccountNotFound::class.simpleName
        }

        @ParameterizedTest
        @EnumSource(
            value = UtilityAccountConnectionStatus::class,
            mode = EnumSource.Mode.EXCLUDE,
            names = ["CONNECTION_ERROR", "DISCONNECTION_ERROR"],
        )
        fun `deve retornar BAD_REQUEST quando a account utility não estiver em um estado que possa ser retentada`(status: UtilityAccountConnectionStatus) {
            repository.save(
                UtilityAccountFixture.create(
                    id = UtilityAccountId("utilityAccountId"),
                    accountId = AccountId("accountId"),
                    walletId = WalletId("walletId"),
                    status = status,
                ),
            )

            val response = controller.utilityAccountRetry(
                walletId = "walletId",
                utilityAccountId = "utilityAccountId",
            )

            response.status() shouldBe HttpStatus.BAD_REQUEST
            response.body().toString() shouldBe UtilityAccountError.CannotRetryAccount::class.simpleName
        }
    }

    @ParameterizedTest
    @MethodSource("retryableStatuses")
    fun `deve retornar OK quando a account utility estiver em um estado que possa ser retentada`(
        status: UtilityAccountConnectionStatus,
        expectedStatus: UtilityAccountConnectionStatus,
    ) {
        repository.save(
            UtilityAccountFixture.create(
                id = UtilityAccountId("utilityAccountId"),
                accountId = AccountId("accountId"),
                walletId = WalletId("walletId"),
                status = status,
            ),
        )

        val response = controller.utilityAccountRetry(
            walletId = "walletId",
            utilityAccountId = "utilityAccountId",
        )

        response.status() shouldBe HttpStatus.OK
        val utility = response.body() as BackofficeUtilityAccountTO
        utility.id shouldBe "utilityAccountId"
        utility.status shouldBe expectedStatus

        repository.findById(
            UtilityAccountId("utilityAccountId"),
            WalletId("walletId"),
        )!!.status shouldBe expectedStatus

        verify {
            sqsMessagePublisher.sendMessage(any())
        }
    }

    companion object {
        @JvmStatic
        fun retryableStatuses() = listOf(
            Arguments.of(
                UtilityAccountConnectionStatus.CONNECTION_ERROR,
                UtilityAccountConnectionStatus.PENDING,
            ),
            Arguments.of(
                UtilityAccountConnectionStatus.DISCONNECTION_ERROR,
                UtilityAccountConnectionStatus.PENDING_DISCONNECTION,
            ),
        )
    }
}