package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.accountRegisterData
import ai.friday.billpayment.accountRegisterDataWithEmailVerified
import ai.friday.billpayment.accountRegisterWithEmailNotVerified
import ai.friday.billpayment.adapters.s3.S3LinkGenerator
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountIsBlockedForEdition
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.BasicAccountRegisterData
import ai.friday.billpayment.app.account.BasicRegisterService
import ai.friday.billpayment.app.account.InvalidTokenException
import ai.friday.billpayment.app.account.InvalidTokenReason
import ai.friday.billpayment.app.account.IssuedToken
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.TokenChannel
import ai.friday.billpayment.app.account.TokenStillValidException
import ai.friday.billpayment.app.account.toBasicAccountRegisterData
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.integration.SecurityFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import arrow.core.Either
import arrow.core.right
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.time.Duration
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class RegisterControllerUpdateEmailTest(embeddedServer: EmbeddedServer) {
    private val securityFixture = SecurityFixture()

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(BasicRegisterService::class)
    fun registerService() = registerService
    private val registerService: BasicRegisterService = mockk() {
        every {
            processLiveness(any())
        } answers {
            firstArg<BasicAccountRegisterData>().right()
        }
    }

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(AccountService::class)
    fun accountService() = accountService
    private val accountService: AccountService = mockk()

    @MockBean(SystemActivityService::class)
    fun getSystemActivityService() = systemActivityService
    private val systemActivityService: SystemActivityService = mockk()

    @MockBean(S3LinkGenerator::class)
    fun getS3LinkGenerator() = s3LinkGenerator
    private val s3LinkGenerator: S3LinkGenerator = mockk()

    private val emailAddress = EmailAddress("<EMAIL>")
    private val emailRequest = EmailRequestTO(email = emailAddress.value)

    private fun buildRegisterEmailRequest(
        emailRequestTO: EmailRequestTO = emailRequest,
        cookie: Cookie,
    ): MutableHttpRequest<EmailRequestTO> {
        return HttpRequest.POST("/basicRegister/emailAddress", emailRequestTO)
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private fun buildTokenValidationRequest(token: String, cookie: Cookie): MutableHttpRequest<TokenValidationTO> {
        return HttpRequest.PUT("/basicRegister/emailAddress/token", TokenValidationTO(token))
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private fun buildGetAccountRegisterRequest(cookie: Cookie): MutableHttpRequest<Unit> {
        return HttpRequest.GET<Unit>("/basicRegister")
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    @Test
    fun `should return forbidden for non guest user`() {
        val request = buildRegisterEmailRequest(
            cookie = securityFixture.cookieAuthOwner,
        )
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(TokenIssuedTO::class.java), Argument.of(ResponseTO::class.java))
        }
        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return internal server error on service unavailable`() {
        val request =
            buildRegisterEmailRequest(
                cookie = securityFixture.cookieMsisdnAuth,
            )

        every { registerService.issueToken(emailAddress, any(), any()) } returns Either.Left(Exception())

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(TokenIssuedTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe RegisterErrors.GENERIC_ERROR.code
    }

    @Test
    fun `should return conflict when valid email token already exists`() {
        val request =
            buildRegisterEmailRequest(
                cookie = securityFixture.cookieMsisdnAuth,
            )

        every { registerService.issueToken(emailAddress, any(), any()) } returns Either.Left(
            TokenStillValidException(
                accountId = ACCOUNT.accountId,
                duration = Duration.ofSeconds(30),
                cooldown = Duration.ofSeconds(20),
            ),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(TokenIssuedTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.CONFLICT
        thrown.response.getBody(TokenIssuedTO::class.java).get() shouldBe TokenIssuedTO(duration = "30", cooldown = "20")
    }

    @Test
    @EnumSource(TokenChannel::class)
    fun `should return 200 and token expiration time when email token is created`() {
        val request =
            buildRegisterEmailRequest(
                cookie = securityFixture.cookieMsisdnAuth,
            )

        every { registerService.issueToken(emailAddress, any(), any()) } answers {
            Either.Right(
                IssuedToken(
                    duration = Duration.ofSeconds(120),
                    accountId = secondArg(),
                    cooldown = Duration.ofSeconds(20),
                ),
            )
        }

        val response = client.toBlocking()
            .exchange(request, Argument.of(TokenIssuedTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.CREATED
        response.body() shouldBe TokenIssuedTO(duration = "120", cooldown = "20")
    }

    @Test
    fun `should return 200 when email token is valid`() {
        val request = buildTokenValidationRequest("123456", securityFixture.cookieMsisdnAuth)

        every {
            registerService.updateMsisdnEmail(
                any(),
            )
        } returns accountRegisterDataWithEmailVerified.toBasicAccountRegisterData().right()
        every { registerService.updateAgreementFiles(any(), any()) } returns accountRegisterDataWithEmailVerified

        val response = client.toBlocking()
            .exchange(request, Argument.of(BasicAccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        response.body()!!.emailVerification?.email shouldBe accountRegisterDataWithEmailVerified.emailAddress.value
        response.body()!!.emailVerification?.isVerified shouldBe true
    }

    @Test
    fun `should return BAD_REQUEST when email token is valid but account is not editable`() {
        val request = buildTokenValidationRequest("123456", securityFixture.cookieMsisdnAuth)

        every { registerService.updateMsisdnEmail(any()) } returns Either.Left(AccountIsBlockedForEdition())

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(BasicAccountRegisterDataTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java)
            .get().code shouldBe RegisterErrors.REGISTER_IS_BLOCKED_FOR_EDITION.code
    }

    @ParameterizedTest
    @MethodSource("invalidTokenErrors")
    fun `should return 400 when email token is not valid`(
        reason: InvalidTokenReason,
        registerErrors: RegisterErrors,
    ) {
        val request = buildTokenValidationRequest("123456", securityFixture.cookieMsisdnAuth)

        every { registerService.updateMsisdnEmail(any()) } returns Either.Left(InvalidTokenException(reason))

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(BasicAccountRegisterDataTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe registerErrors.code
        thrown.response.getBody(ResponseTO::class.java)
            .get().message shouldBe registerErrors.description
    }

    @Test
    fun `should return 500 when generic exception happens`() {
        val request = buildTokenValidationRequest("123456", securityFixture.cookieMsisdnAuth)

        every { registerService.updateMsisdnEmail(any()) } returns Either.Left(Exception())

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(BasicAccountRegisterDataTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )
        }

        thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe RegisterErrors.GENERIC_ERROR.code
    }

    @Test
    fun `should return email and expiration in get register when user has a email to validate`() {
        val request = buildGetAccountRegisterRequest(securityFixture.cookieMsisdnAuth)
        val now = getZonedDateTime()
        val expiration = now.plusMinutes(1).toEpochSecond()

        val accountRegisterData = accountRegisterData.copy(
            emailAddress = emailAddress,
            emailVerified = false,
            emailTokenExpiration = expiration,
        )
        every { registerService.findByAccountId(any()) } returns Either.Right(accountRegisterData.toBasicAccountRegisterData())
        every { registerService.updateAgreementFiles(any(), any()) } returns accountRegisterData

        val response = withGivenDateTime(now) {
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(BasicAccountRegisterDataTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )
        }

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            assertSoftly {
                emailVerification?.email shouldBe emailAddress.value
                emailVerification?.isVerified shouldBe false
                emailVerification?.tokenExpiration shouldBe expiration
            }
        }
    }

    @Test
    fun `should not return email and expiration in get register when token expiration is null`() {
        val request = buildGetAccountRegisterRequest(securityFixture.cookieMsisdnAuth)

        val accountRegisterData = accountRegisterData.copy(
            emailAddress = emailAddress,
            emailVerified = false,
            emailTokenExpiration = null,
        )
        every { registerService.findByAccountId(any()) } returns Either.Right(accountRegisterData.toBasicAccountRegisterData())
        every { registerService.updateAgreementFiles(any(), any()) } returns accountRegisterData

        val response = client.toBlocking()
            .exchange(request, Argument.of(BasicAccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            emailVerification.shouldBeNull()
        }
    }

    @Test
    fun `deve retornar um account register de um usuário sem email verificado`() {
        val request = buildGetAccountRegisterRequest(securityFixture.cookieMsisdnAuth)

        every { registerService.findByAccountId(any()) } returns Either.Right(accountRegisterWithEmailNotVerified.toBasicAccountRegisterData())
        every { registerService.updateAgreementFiles(any(), any()) } returns accountRegisterWithEmailNotVerified

        val response = client.toBlocking()
            .exchange(request, Argument.of(BasicAccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            assertSoftly {
                email shouldBe null
                emailVerification shouldBe null
            }
        }
    }

    companion object {

        @JvmStatic
        fun invalidTokenErrors(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(InvalidTokenReason.NOT_FOUND, RegisterErrors.TOKEN_NOT_FOUND),
                Arguments.of(InvalidTokenReason.MISMATCH, RegisterErrors.TOKEN_MISMATCH),
                Arguments.of(InvalidTokenReason.EXPIRED, RegisterErrors.TOKEN_EXPIRED),
                Arguments.of(InvalidTokenReason.MAX_ATTEMPTS, RegisterErrors.TOKEN_MAX_ATTEMPTS),
            )
        }
    }
}