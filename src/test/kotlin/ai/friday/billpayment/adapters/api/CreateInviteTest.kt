package ai.friday.billpayment.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.WalletConfiguration
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.CustomPermissionsMemberType
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.InviteStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.buildInvite
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.EMAIL
import ai.friday.billpayment.integration.NAME
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.generateCookie
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.withGivenDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource

@MicronautTest(environments = [FRIDAY_ENV])
class CreateInviteTest(
    embeddedServer: EmbeddedServer,
    val configuration: WalletConfiguration,
    val dynamoDB: AmazonDynamoDB,
) {

    private val enhancedClient = getDynamoDB()

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(WalletRepository::class)
    fun walletRepository(): WalletRepository = walletRepository

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository: WalletRepository = spyk(
        WalletDbRepository(
            walletDAO = walletDAO,
            walletMemberDAO = walletMemberDAO,
            inviteDAO = inviteDAO,
            inviteReminderDAO = inviteReminderDAO,
            walletLimitDAO = walletLimitDAO,
            accountRepository = mockk(),
        ),
    )

    @MockBean(NotificationAdapter::class)
    fun notificationAdapter(): NotificationAdapter = notificationAdapter
    private val notificationAdapter: NotificationAdapter = mockk(relaxUnitFun = true)

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val accountId = wallet.founder.accountId
    private val walletId = wallet.id
    private val founder = wallet.founder
    private val walletWithOneMaxInvitation = wallet.copy(maxOpenInvitations = 1)

    private fun buildInviteRequest(
        cookie: Cookie,
        walletId: WalletId,
        inviteRequestTO: InviteRequestTO,
    ): MutableHttpRequest<InviteRequestTO> {
        return HttpRequest.POST("/wallet/${walletId.value}/invite", inviteRequestTO)
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `should return bad request on invalid document`() {
        val inviteRequestTO = InviteRequestTO(
            document = "**********",
            name = "Enzo",
            email = "<EMAIL>",
            type = CustomPermissionsMemberType.COLLABORATOR,
        )

        val request = buildInviteRequest(generateCookie(accountId, Role.OWNER), walletId, inviteRequestTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @ParameterizedTest
    @CsvSource(value = ["@email.com", "fakeemail.com", "fake@", "fake@<EMAIL>", "<EMAIL>"])
    fun `should return bad request on invalid email`(email: String) {
        val inviteRequestTO = InviteRequestTO(
            document = "***********",
            name = "Enzo",
            email = email,
            type = CustomPermissionsMemberType.COLLABORATOR,
        )

        val request = buildInviteRequest(generateCookie(accountId, Role.OWNER), walletId, inviteRequestTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `should return not found when wallet ID does not exist`() {
        every {
            walletRepository.findWallet(walletId)
        } throws ItemNotFoundException("wallet não encontrada")

        val inviteRequestTO = InviteRequestTO(
            document = "***********",
            name = "Enzo",
            email = "<EMAIL>",
            type = CustomPermissionsMemberType.COLLABORATOR,
        )

        val request = buildInviteRequest(generateCookie(accountId, Role.OWNER), walletId, inviteRequestTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe InviteErrorsResponse.WalletNotFound.code
    }

    @Test
    fun `should return forbidden when authenticated user is not wallet founder`() {
        every {
            walletRepository.findWallet(walletId)
        } returns wallet

        val inviteRequestTO = InviteRequestTO(
            document = "***********",
            name = "Enzo",
            email = "<EMAIL>",
            type = CustomPermissionsMemberType.COLLABORATOR,
        )

        val request = buildInviteRequest(generateCookie(AccountId(ACCOUNT_ID_2), Role.OWNER), walletId, inviteRequestTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return bad request on open invitations exceeded`() {
        every {
            walletRepository.findWallet(walletId)
        } returns walletWithOneMaxInvitation

        val invite = Invite(
            walletId = walletId,
            memberDocument = "***********",
            memberName = "Alexandre",
            memberType = MemberType.COLLABORATOR,
            status = InviteStatus.PENDING,
            validUntil = getLocalDate(),
            walletName = walletWithOneMaxInvitation.name,
            founderName = founder.name,
            founderDocument = founder.document,
            created = getZonedDateTime(),
        )

        every {
            walletRepository.findInvitesFrom(walletId, any())
        } returns listOf(invite)

        val inviteRequestTO = InviteRequestTO(
            document = "***********",
            name = "Enzo",
            email = "<EMAIL>",
            type = CustomPermissionsMemberType.COLLABORATOR,
        )

        val request = buildInviteRequest(generateCookie(accountId, Role.OWNER), walletId, inviteRequestTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java)
            .get().code shouldBe InviteErrorsResponse.PendingInvitationsLimitReached.code
    }

    @Test
    fun `should return bad request when invited user is already a member`() {
        every {
            walletRepository.findWallet(walletId)
        } returns wallet

        every {
            walletRepository.findInvitesFrom(walletId, any())
        } returns listOf()

        val inviteRequestTO = InviteRequestTO(
            document = "***********",
            name = "Enzo",
            email = "<EMAIL>",
            type = CustomPermissionsMemberType.COLLABORATOR,
        )

        val request = buildInviteRequest(generateCookie(accountId, Role.OWNER), walletId, inviteRequestTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe InviteErrorsResponse.UserAlreadyMember.code
    }

    @Test
    fun `should return conflict and invite created when invite already exists`() {
        every {
            walletRepository.findWallet(walletId)
        } returns wallet

        val invite = Invite(
            walletId = walletId,
            memberDocument = "***********",
            memberName = "Alexandre",
            memberType = MemberType.COLLABORATOR,
            status = InviteStatus.PENDING,
            validUntil = getLocalDate(),
            walletName = wallet.name,
            founderName = founder.name,
            founderDocument = founder.document,
            created = getZonedDateTime(),
        )

        every {
            walletRepository.findInvitesFrom(walletId, any())
        } returns listOf(invite)

        val inviteRequestTO = InviteRequestTO(
            document = invite.memberDocument,
            name = invite.memberName,
            type = CustomPermissionsMemberType.COLLABORATOR,
            email = "<EMAIL>",
        )

        val request = buildInviteRequest(generateCookie(accountId, Role.OWNER), walletId, inviteRequestTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(InviteResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.CONFLICT
        with(thrown.response.getBody(InviteResponseTO::class.java).get()) {
            this.walletId shouldBe invite.walletId.value
            this.memberDocument shouldBe invite.memberDocument
            this.memberName shouldBe invite.memberName
            this.status shouldBe invite.status.name
        }

        verify(exactly = 0) {
            walletRepository.save(ofType(Invite::class))
            notificationAdapter.notifyInvitedMember(invite, EmailAddress(inviteRequestTO.email))
        }
    }

    @ParameterizedTest
    @EnumSource(InviteStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PENDING"])
    fun `não deve sobrescrever convites em estado final`(inviteStatus: InviteStatus) {
        every {
            walletRepository.findWallet(walletId)
        } returns wallet

        walletRepository.save(buildInvite(inviteStatus = inviteStatus))

        val inviteRequestTO = InviteRequestTO(
            document = DOCUMENT,
            type = CustomPermissionsMemberType.ASSISTANT,
            name = NAME,
            email = EMAIL,
        )

        val request = buildInviteRequest(generateCookie(accountId, Role.OWNER), wallet.id, inviteRequestTO)

        client.toBlocking()
            .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))

        val invites = walletRepository.findInvitesFrom(wallet.id, configuration.inviteExpiration)

        invites shouldHaveSize 2
    }

    @ParameterizedTest
    @EnumSource(InviteStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PENDING"])
    fun `should create invite when there is an invite not pending`() {
        every {
            walletRepository.findWallet(walletId)
        } returns wallet

        val expiredInvite = Invite(
            walletId = walletId,
            memberDocument = "***********",
            memberName = "Alexandre",
            memberType = MemberType.COLLABORATOR,
            status = InviteStatus.EXPIRED,
            validUntil = getLocalDate().minusDays(1),
            walletName = wallet.name,
            founderName = founder.name,
            founderDocument = founder.document,
            created = getZonedDateTime().minusDays(8),
        )
        every {
            walletRepository.findInvitesFrom(walletId, any())
        } returns listOf(expiredInvite)

        val inviteRequestTO = InviteRequestTO(
            document = "***********",
            name = "Alexandre",
            type = CustomPermissionsMemberType.ASSISTANT,
            email = "<EMAIL>",
        )

        val request = buildInviteRequest(generateCookie(accountId, Role.OWNER), walletId, inviteRequestTO)

        val startZonedDate = ZonedDateTime.of(2021, 1, 30, 0, 0, 0, 0, brazilTimeZone)
        withGivenDateTime(startZonedDate) {
            val response = client.toBlocking()
                .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.CREATED
            with(response.body()!!) {
                memberDocument shouldBe inviteRequestTO.document
                memberName shouldBe inviteRequestTO.name
                memberType shouldBe MemberType.ASSISTANT
                status shouldBe InviteStatus.PENDING.name
                validUntil shouldBe getLocalDate().plusDays(configuration.inviteExpiration.toDays()).format(dateFormat)
                walletName shouldBe wallet.name
                founderName shouldBe founder.name
                founderDocument shouldBe founder.document
            }

            val invite = walletRepository.findInvite(walletId, inviteRequestTO.document)

            invite.memberDocument shouldBe inviteRequestTO.document
            invite.memberName shouldBe inviteRequestTO.name
            invite.memberType shouldBe MemberType.ASSISTANT
            invite.status shouldBe InviteStatus.PENDING
            invite.validUntil shouldBe getLocalDate().plusDays(configuration.inviteExpiration.toDays())
            invite.walletName shouldBe wallet.name
            invite.founderName shouldBe founder.name
            invite.founderDocument shouldBe founder.document
            invite.created shouldBe startZonedDate
            invite.updatedAt shouldBe startZonedDate

            verify {
                walletRepository.save(ofType(Invite::class))
                notificationAdapter.notifyInvitedMember(invite, EmailAddress(inviteRequestTO.email))
            }
        }
    }

    @Test
    fun `should create invite when document is valid`() {
        every {
            walletRepository.findWallet(walletId)
        } returns wallet

        every {
            walletRepository.findInvitesFrom(walletId, any())
        } returns listOf()

        val inviteRequestTO = InviteRequestTO(
            document = "***********",
            name = "Alexandre",
            type = CustomPermissionsMemberType.ASSISTANT,
            email = "<EMAIL>",
        )

        val request = buildInviteRequest(generateCookie(accountId, Role.OWNER), walletId, inviteRequestTO)

        val startZonedDate = ZonedDateTime.of(2021, 1, 30, 0, 0, 0, 0, brazilTimeZone)
        withGivenDateTime(startZonedDate) {
            val response = client.toBlocking()
                .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.CREATED
            with(response.body()!!) {
                memberDocument shouldBe inviteRequestTO.document
                memberName shouldBe inviteRequestTO.name
                memberType shouldBe MemberType.ASSISTANT
                status shouldBe InviteStatus.PENDING.name
                validUntil shouldBe getLocalDate().plusDays(configuration.inviteExpiration.toDays())
                    .format(dateFormat)
                walletName shouldBe wallet.name
                founderName shouldBe founder.name
                founderDocument shouldBe founder.document
            }

            val invite = walletRepository.findInvite(walletId, inviteRequestTO.document)

            invite.memberDocument shouldBe inviteRequestTO.document
            invite.memberName shouldBe inviteRequestTO.name
            invite.memberType shouldBe MemberType.ASSISTANT
            invite.status shouldBe InviteStatus.PENDING
            invite.validUntil shouldBe getLocalDate().plusDays(configuration.inviteExpiration.toDays())
            invite.walletName shouldBe wallet.name
            invite.founderName shouldBe founder.name
            invite.founderDocument shouldBe founder.document
            invite.created shouldBe startZonedDate
            invite.updatedAt shouldBe startZonedDate

            verify {
                walletRepository.save(ofType(Invite::class))
                notificationAdapter.notifyInvitedMember(invite, EmailAddress(inviteRequestTO.email))
            }
        }
    }

    @Test
    fun `should return created when inviting an assistant`() {
        every {
            walletRepository.findWallet(walletId)
        } returns wallet

        every {
            walletRepository.findInvitesFrom(walletId, any())
        } returns listOf()

        val inviteRequestTO = InviteRequestTO(
            document = "***********",
            type = CustomPermissionsMemberType.ASSISTANT,
            name = "Fulano",
            email = "<EMAIL>",
        )

        val request = buildInviteRequest(generateCookie(accountId, Role.OWNER), walletId, inviteRequestTO)

        val response = client.toBlocking()
            .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.CREATED

        with(response.body()!!) {
            founderDocument shouldBe wallet.founder.document
            founderName shouldBe wallet.founder.name
            memberDocument shouldBe inviteRequestTO.document
            memberName shouldBe inviteRequestTO.name
            memberType shouldBe MemberType.ASSISTANT
            walletId shouldBe wallet.id.value
            walletName shouldBe wallet.name
            status shouldBe InviteStatus.PENDING.name
        }
    }
}