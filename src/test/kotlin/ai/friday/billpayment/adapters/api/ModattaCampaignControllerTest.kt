package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.modatta.ModattaCampaignAdapter
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.campaigns.RedeemCodeResult
import ai.friday.billpayment.app.journey.UserJourneyEvent
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.integration.buildCookie
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class ModattaCampaignControllerTest(
    embeddedServer: EmbeddedServer,
) {
    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)
    private val cookie = buildCookie(ACCOUNT)

    @MockBean(ModattaCampaignAdapter::class)
    fun modattaCampaignAdapter() = modattaCampaignAdapter
    private val modattaCampaignAdapter: ModattaCampaignAdapter = mockk()

    @MockBean(AccountService::class)
    fun accountService() = accountService
    private val accountService: AccountService = mockk()

    @MockBean(UserJourneyService::class)
    fun userJourneyService() = userJourneyService
    private val userJourneyService: UserJourneyService = mockk()

    @Test
    fun `deve retornar erro 500 se for um erro não tratado, e não deve tentar atualizar o usuário no intercom e o trackear o evento no UserPilot`() {
        every {
            modattaCampaignAdapter.redeemCode(any())
        } returns IllegalStateException("foo").left()

        val request = buildRequest()

        val response = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }

        response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR

        verify(exactly = 0) {
            accountService.addGroupsToAccount(any<AccountId>(), any())
            userJourneyService.trackEventAsync(any(), any())
        }
    }

    @Test
    fun `deve retornar sucesso se for um código já resgatado, mas não deve tentar atualizar o usuário no intercom e o trackear o evento no UserPilot`() {
        every {
            modattaCampaignAdapter.redeemCode(any())
        } returns RedeemCodeResult.ALREADY_REDEEMED.right()

        val request = buildRequest()

        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK

        verify(exactly = 0) {
            accountService.addGroupsToAccount(any<AccountId>(), any())
            userJourneyService.trackEventAsync(any(), any())
        }
    }

    @Test
    fun `deve retornar sucesso quando for um código não encontrado, mas não deve tentar atualizar o usuário no intercom e o trackear o evento no UserPilot`() {
        every {
            modattaCampaignAdapter.redeemCode(any())
        } returns RedeemCodeResult.INVALID_CODE.right()

        val request = buildRequest()

        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK

        verify(exactly = 0) {
            accountService.addGroupsToAccount(any<AccountId>(), any())
            userJourneyService.trackEventAsync(any(), any())
        }
    }

    @Test
    fun `deve retornar sucesso quando o código for resgate, deve tentar atualizar o usuário no intercom e o trackear o evento no UserPilot`() {
        every {
            modattaCampaignAdapter.redeemCode(any())
        } returns RedeemCodeResult.REDEEMED.right()

        val request = buildRequest()

        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK

        val accountGroupsSlot = slot<List<AccountGroup>>()

        verify(exactly = 1) {
            accountService.addGroups(ACCOUNT.accountId, capture(accountGroupsSlot))
            userJourneyService.trackEventAsync(ACCOUNT.accountId, UserJourneyEvent.RedeemModattaCampaign)
        }

        accountGroupsSlot.captured shouldContain AccountGroup.MODATTA_MISSION_CAMPAIGN
    }

    private fun buildRequest() = HttpRequest.GET<String>("/modatta/campaign/redeem/123")
        .cookie(cookie).header("X-API-VERSION", "2")
}