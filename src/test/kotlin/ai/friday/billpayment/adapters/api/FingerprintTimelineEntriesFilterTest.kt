import ai.friday.billpayment.adapters.api.FingerprintFilter
import ai.friday.billpayment.app.security.Fingerprint
import ai.friday.billpayment.app.security.FingerprintApp
import ai.friday.billpayment.app.security.FingerprintDevice
import ai.friday.billpayment.app.security.FingerprintGeolocation
import ai.friday.billpayment.app.security.FingerprintOS
import io.kotest.matchers.equality.shouldBeEqualToComparingFields
import io.micronaut.http.HttpRequest
import io.micronaut.http.MutableHttpHeaders
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.filter.ServerFilterChain
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class FingerprintTimelineEntriesFilterTest {

    private val chain: ServerFilterChain = mockk()

    lateinit var filter: FingerprintFilter

    private val expectedFingerprint = Fingerprint(
        sessionId = null,
        app = FingerprintApp(version = "11.0.90"),
        geolocation = FingerprintGeolocation(latitude = "-31.749952", longitude = "-52.331335"),
        device = FingerprintDevice(
            manufacturer = "Apple",
            model = "iPhone12,1",
            installationId = "D10E4BF0-489E-48AF-BD14-B1F5A8E197FF",
        ),
        os = FingerprintOS(name = "ios", version = "16.1.1"),
    )

    @BeforeEach
    fun setup() {
        filter = FingerprintFilter()
    }

    @Test
    fun `should set 'X-Fingerprint' attribute when 'fingerprint' header is present`() {
        val fingerprintHeaderValue =
            "ewogICJuZXdfYXBpX2lvcyIgOiAidHJ1ZSIsCiAgIm9zX3ZlcnNpb24iIDogIjE2LjEuMSIsCiAgImxvY2F0aW9uX3RpbWVzdGFtcCIgOiAiMTY4ODQ4MDc3MDE0NSIsCiAgImRldmljZV9pZCIgOiAiQ0U3ODI5QUItMERFMC00NDU2LUI2OTItNkI1QjFEQzUyMEUwIiwKICAiWC1SZXF1ZXN0LUlEIiA6ICI0NjU2MzU4Ni00MDJCLTRGQzAtQTUxQi03QTQ0QTlBMUI2NjkiLAogICJkZXZpY2VfbWFudWZhY3R1cmVyIiA6ICJBcHBsZSIsCiAgImN1cnJlbnRfdGltZXN0YW1wIiA6ICIxNjg4NDgwNzc5Nzg2IiwKICAiaW5zdGFsbGF0aW9uX2lkIiA6ICJEMTBFNEJGMC00ODlFLTQ4QUYtQkQxNC1CMUY1QThFMTk3RkYiLAogICJncHNfYWNjIiA6ICIxMC4wNTEyMDE5Nzc1NTM2OTQiLAogICJhcHBfdmVyc2lvbl9iIiA6ICIxMS4wLjkwLjE1NTY5MSIsCiAgImxvbmdpdHVkZSIgOiAiLTUyLjMzMTMzNSIsCiAgInRpbWV6b25lIiA6ICJBbWVyaWNhXC9TYW9fUGF1bG8iLAogICJkZXZpY2Vfb3MiIDogImlvcyIsCiAgImFwcF92ZXJzaW9uIiA6ICIxMS4wLjkwIiwKICAiZGV2aWNlX21vZGVsIiA6ICJpUGhvbmUxMiwxIiwKICAibGF0aXR1ZGUiIDogIi0zMS43NDk5NTIiCn0="
        val request = createMockRequestWithHeader("fingerprint", fingerprintHeaderValue)

        every { chain.proceed(any()) } returns mockk()

        filter.doFilter(request, chain)

        verify {
            request.setAttribute(
                "X-Fingerprint",
                withArg<Fingerprint> {
                    it shouldBeEqualToComparingFields expectedFingerprint
                },
            )
        }
    }

    @Test
    fun `should not set 'X-Fingerprint' attribute when 'fingerprint' header is not present`() {
        val request = createMockRequestWithHeader("otherHeader", "someValue")
        every { request.headers.contains("fingerprint") } returns false
        every { chain.proceed(any()) } returns mockk()

        filter.doFilter(request, chain)

        verify { request.setAttribute("X-Fingerprint", null) }
    }

    private fun createMockRequestWithHeader(headerName: String, headerValue: String): HttpRequest<*> {
        val request = mockk<MutableHttpRequest<*>>()
        val headers = mockk<MutableHttpHeaders>()
        every { request.headers } returns headers
        every { headers.contains(headerName) } returns true
        every { headers[headerName] } returns headerValue
        every { request.setAttribute(any(), any()) } returns request
        return request
    }
}