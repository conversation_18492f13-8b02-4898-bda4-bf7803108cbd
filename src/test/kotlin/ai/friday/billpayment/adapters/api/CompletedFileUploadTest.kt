package ai.friday.billpayment.adapters.api

import io.kotest.matchers.shouldBe
import io.micronaut.http.MediaType
import io.micronaut.http.multipart.CompletedFileUpload
import io.mockk.every
import io.mockk.mockk
import java.util.Optional
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

internal class CompletedFileUploadTest {

    @ParameterizedTest
    @ValueSource(strings = [MediaType.IMAGE_JPEG, MediaType.IMAGE_PNG, MediaType.TEXT_PLAIN])
    fun `should return extension when media type is valid list`(mediaTypeString: String) {
        val completedFileUploadMock = mockk<CompletedFileUpload>()
        val mediaType = MediaType.of(mediaTypeString)

        every { completedFileUploadMock.contentType } returns Optional.of(mediaType)

        completedFileUploadMock.fileExtension(valid = listOf(mediaType)) shouldBe mediaType.extension
    }

    @Test
    fun `should return empty when media type is not a default valid type`() {
        val completedFileUploadMock = mockk<CompletedFileUpload>()

        every { completedFileUploadMock.contentType } returns Optional.of(MediaType.TEXT_PLAIN_TYPE)

        completedFileUploadMock.fileExtension() shouldBe null
    }
}