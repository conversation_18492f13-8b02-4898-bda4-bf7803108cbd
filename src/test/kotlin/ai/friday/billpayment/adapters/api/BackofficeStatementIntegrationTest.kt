package ai.friday.billpayment.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.arbi.ArbiAccountStatementAdapter
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.integrations.AccountStatementAdapter
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.integration.SecurityFixture
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDate
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
internal class BackofficeStatementIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {
    private val securityFixture = SecurityFixture()

    @MockBean(InternalBankRepository::class)
    fun internalBankRepository() = internalBankRepository
    private val internalBankRepository = mockk<InternalBankRepository>()

    @MockBean(ArbiAccountStatementAdapter::class)
    fun accountStatementAdapter() = accountStatementAdapter
    private val accountStatementAdapter = mockk<AccountStatementAdapter> {
        every {
            getStatement(any(), any(), any(), any())
        } returns BankStatement(items = listOf(), initialBalance = Balance(15591 * 2), finalBalance = Balance(123 * 2))
    }

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk {
        every {
            findWalletCategories(any())
        } returns emptyList()
    }

    private val enhancedClient = getDynamoDB()
    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = accountRepository,
    )

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        loadAccountIntoDb(dynamoDB, hasCreditCard = false, accountId = wallet.founder.accountId)
        loadBalancePaymentMethod(accountRepository, accountId = wallet.founder.accountId.value, paymentMethodId = wallet.paymentMethodId.value)
        walletRepository.save(wallet)
    }

    @Test
    fun `deve retornar BAD_REQUEST quando não encontrar um paymentMethod`() {
        every {
            internalBankRepository.findAllBankStatementCredits(any(), any(), any())
        } returns emptyList()

        val request =
            HttpRequest.GET<Any>("/backoffice/statement/wallet/1234444?startDate=2022-11-01&endDate=2022-11-10")
                .cookie(securityFixture.cookieBackoffice)

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `deve retornar OK quando gerar o CSV com conteudo`() {
        every {
            internalBankRepository.findAllBankStatementCredits(any(), any(), any())
        } returns listOf(
            DefaultBankStatementItem(
                date = LocalDate.now(),
                flow = BankStatementItemFlow.CREDIT,
                type = BankStatementItemType.PIX,
                description = "fake",
                operationNumber = "-1",
                isTemporaryOperationNumber = false,
                amount = 1_23,
                counterpartName = "nome de quem depositou",
                counterpartDocument = "***********",
                counterpartAccountNo = "12345",
                documentNumber = "***********",
                ref = "ref",
                lastUpdate = ZonedDateTime.now(),
            ),
            DefaultBankStatementItem(
                date = LocalDate.now(),
                flow = BankStatementItemFlow.CREDIT,
                type = BankStatementItemType.PIX,
                description = "fake",
                operationNumber = "-1",
                isTemporaryOperationNumber = false,
                amount = 3_21,
                counterpartName = "nome de quem depositou",
                counterpartDocument = "***********",
                counterpartAccountNo = "12345",
                documentNumber = "***********",
                ref = "ref",
                lastUpdate = ZonedDateTime.now(),
            ),
        )

        val request =
            HttpRequest.GET<Any>("/backoffice/statement/wallet/${wallet.id.value}?startDate=2022-11-01&endDate=2022-11-10")
                .cookie(securityFixture.cookieBackoffice)

        val response = client.toBlocking().exchange(request, String::class.java)

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            contains("valor,nome,data,descrição,tipo,saldo")
            contains("R$ 1,23")
            contains("R$ 3,21")
        }
    }

    @Test
    fun `deve retornar OK quando gerar o CSV sem conteudo`() {
        every {
            internalBankRepository.findAllBankStatementCredits(any(), any(), any())
        } returns emptyList()

        val request =
            HttpRequest.GET<Any>("/backoffice/statement/wallet/${wallet.id.value}?startDate=2022-11-01&endDate=2022-11-10")
                .cookie(securityFixture.cookieBackoffice)

        val response = client.toBlocking().exchange(request, String::class.java)

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            println(this)
            contains("valor,nome,data,descrição,tipo,saldo")
        }
    }
}