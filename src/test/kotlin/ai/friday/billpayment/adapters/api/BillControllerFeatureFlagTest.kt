package ai.friday.billpayment.adapters.api

import DynamoDBUtils
import ai.friday.billpayment.adapters.arbi.ArbiValidationResponse
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.FeaturesDbRepository
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.micronaut.FeaturesConfigurationMicronaut
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.earlyAccessAccounts
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.CreateBoletoRequest
import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.MaintenanceServicesEnum
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.fichaRegisterData
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.integration.onWallet
import ai.friday.billpayment.successConcessionariaValidationResponse
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class BillControllerFeatureFlagTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
) {
    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    private val featureRepositoryMock = mockk<FeaturesRepository>(relaxed = true)

    private val featureConfigurationMock = mockk<FeatureConfiguration>(relaxed = true)

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val billAddedFichaEvent = billAddedFicha.copy(
        walletId = wallet.id,
        actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
    )

    private val billAdded = ai.friday.billpayment.billAdded.copy(
        walletId = wallet.id,
        actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
    )
    private val billIgnored = ai.friday.billpayment.billIgnored.copy(walletId = wallet.id)

    private val billMarkedAsPaid = ai.friday.billpayment.billMarkedAsPaid.copy(walletId = wallet.id)

    private val billPaymentScheduled =
        ai.friday.billpayment.billPaymentScheduled.copy(walletId = wallet.id)

    private val validFichaCompensacaoTO =
        AddFichaDeCompensacaoTO(digitableLine = FICHA_DE_COMPENSACAO_DIGITABLE_LINE, description = "description")

    private val arbiValidationResponse =
        ArbiValidationResponse(billRegisterData = fichaRegisterData, paymentStatus = 12, resultado = "SUCESSO")

    @field:Property(name = "lock.tableName")
    lateinit var lockTableName: String
    private val dynamoClient = DynamoDBUtils.setupDynamoDB()
    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(dynamoClient),
        refundedClient = RefundedBillDynamoDAO(dynamoClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(dynamoClient),
    )
    private val billEventDAO = BillEventDynamoDAO(dynamoClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(dynamoClient)
    private val transactionDynamo = TransactionDynamo(dynamoClient)
    private val billEventRepository = BillEventDBRepository(
        billEventDAO = billEventDAO,
        uniqueConstraintDAO = uniqueConstraintDAO,
        featureConfiguration = featureConfigurationMock,
        transactionDynamo = transactionDynamo,
    )

    private val accountIdEarlyAccess = earlyAccessAccounts.first()

    @MockBean(FeaturesDbRepository::class)
    fun featureRepository(): FeaturesRepository = featureRepositoryMock

    @MockBean(FeaturesConfigurationMicronaut::class)
    fun featureConfigurations(): FeatureConfiguration = featureConfigurationMock

    @MockBean(WalletDbRepository::class)
    fun walletRepository(): WalletRepository = walletRepository
    private val walletRepository: WalletRepository = mockk() {
        every {
            findWalletOrNull(wallet.id)
        } returns wallet

        every {
            findWallet(wallet.id)
        } returns wallet
    }

    private val validationService: BillValidationService = mockk(relaxed = true)

    @MockBean(BillValidationService::class)
    fun getBillValidationService(): BillValidationService = validationService

    @MockBean(BoletoSettlementService::class)
    fun openBankingMock(): BoletoSettlementService = boletoSettlementMock
    private val boletoSettlementMock: BoletoSettlementService = mockk(relaxed = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        createLockTable(dynamoDB, lockTableName)
    }

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @Nested
    @DisplayName("quando existir a flag BOLETO na lista de serviços em manutenção")
    inner class FooTest {
        @Test
        fun `deve retornar 503 na criação de ficha de compensação`() {
            every { featureRepositoryMock.getAll().maintenanceServices } returns listOf(MaintenanceServicesEnum.BOLETO)
            val request = HttpRequest.POST(
                "/bill/ficha-compensacao",
                validFichaCompensacaoTO.copy(digitableLine = FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
            ).onWallet(wallet)

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))
            }

            thrown.status shouldBe HttpStatus.SERVICE_UNAVAILABLE
        }

        @Test
        fun `não deve retornar 503 na criação de ficha de compensação se o usuário for alpha`() {
            every {
                walletRepository.findWalletOrNull(WalletId(accountIdEarlyAccess.value))
            } returns wallet.copy(members = listOf(wallet.activeMembers.first().copy(accountId = accountIdEarlyAccess)))

            every {
                walletRepository.findWallet(WalletId(accountIdEarlyAccess.value))
            } returns wallet.copy(members = listOf(wallet.activeMembers.first().copy(accountId = accountIdEarlyAccess)))

            every { validationService.validate(ofType(BarCode::class)) } answers {
                arbiValidationResponse.copy(
                    billRegisterData = fichaRegisterData.copy(
                        fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
                        dueDate = getLocalDate(),
                    ),
                )
            }

            val billEvent = billAddedFichaEvent.copy(
                fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
                idNumber = fichaRegisterData.idNumber,
                effectiveDueDate = getLocalDate(),
            )
            billEventRepository.save(billEvent)
            billEventRepository.save(billPaymentScheduled)
            billRepository.save(Bill.build(billEvent, billPaymentScheduled))

            every { featureRepositoryMock.getAll().maintenanceServices } returns listOf(MaintenanceServicesEnum.BOLETO)

            val request = HttpRequest.POST(
                "/bill/ficha-compensacao",
                validFichaCompensacaoTO.copy(digitableLine = FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2),
            ).onWallet(
                wallet.copy(id = WalletId(accountIdEarlyAccess.value)),
                wallet.founder.copy(
                    accountId = accountIdEarlyAccess,
                ),
            )

            val response = client.toBlocking()
                .exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))

            response.status shouldBe HttpStatus.CREATED
        }

        @Test
        fun `deve retornar 503 na reativação de boleto`() {
            every { featureRepositoryMock.getAll().maintenanceServices } returns listOf(MaintenanceServicesEnum.BOLETO)

            every { boletoSettlementMock.validateBill(any<CreateBoletoRequest>()) } answers { successConcessionariaValidationResponse }

            val request = HttpRequest.PUT(
                "/bill/id/${billIgnored.billId.value}/reactivate",
                "",
            ).onWallet(wallet)

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking().exchange(request, BillTO::class.java)
            }

            thrown.status shouldBe HttpStatus.SERVICE_UNAVAILABLE
        }

        @Test
        fun `não deve retornar 503 na reativação de boleto se o usuário for alpha`() {
            every {
                walletRepository.findWalletOrNull(WalletId(accountIdEarlyAccess.value))
            } returns wallet.copy(members = listOf(wallet.activeMembers.first().copy(accountId = accountIdEarlyAccess)))

            every {
                walletRepository.findWallet(WalletId(accountIdEarlyAccess.value))
            } returns wallet.copy(members = listOf(wallet.activeMembers.first().copy(accountId = accountIdEarlyAccess)))

            every { featureRepositoryMock.getAll().maintenanceServices } returns listOf(MaintenanceServicesEnum.BOLETO)

            billEventRepository.save(billAdded)
            billEventRepository.save(billIgnored)

            every { boletoSettlementMock.validateBill(any<CreateBoletoRequest>()) } answers { successConcessionariaValidationResponse }

            val request = HttpRequest.PUT(
                "/bill/id/${billIgnored.billId.value}/reactivate",
                "",
            ).onWallet(
                wallet.copy(id = WalletId(accountIdEarlyAccess.value)),
                wallet.founder.copy(
                    accountId = accountIdEarlyAccess,
                ),
            )

            val response =
                client.toBlocking().exchange(request, BillTO::class.java)

            response.status shouldBe HttpStatus.OK
        }

        @Test
        fun `deve retornar 503 ao desmarcar como pago`() {
            every { featureRepositoryMock.getAll().maintenanceServices } returns listOf(MaintenanceServicesEnum.BOLETO)

            val request = HttpRequest.PUT(
                "/bill/id/${billIgnored.billId.value}/cancel-marked-as-paid",
                "",
            ).onWallet(wallet)

            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking().exchange(request, BillTO::class.java)
            }

            thrown.status shouldBe HttpStatus.SERVICE_UNAVAILABLE
        }

        @Test
        fun `não deve retornar 503 ao desmarcar como pago se o usuário for alpha`() {
            every {
                walletRepository.findWalletOrNull(WalletId(accountIdEarlyAccess.value))
            } returns wallet.copy(members = listOf(wallet.activeMembers.first().copy(accountId = accountIdEarlyAccess)))

            every {
                walletRepository.findWallet(WalletId(accountIdEarlyAccess.value))
            } returns wallet.copy(members = listOf(wallet.activeMembers.first().copy(accountId = accountIdEarlyAccess)))

            every { featureRepositoryMock.getAll().maintenanceServices } returns listOf(MaintenanceServicesEnum.BOLETO)

            billEventRepository.save(billAdded)
            billEventRepository.save(billMarkedAsPaid)

            every { boletoSettlementMock.validateBill(any<Bill>()) } answers { successConcessionariaValidationResponse }

            val request = HttpRequest.PUT(
                "/bill/id/${billMarkedAsPaid.billId.value}/cancel-marked-as-paid",
                "",
            ).onWallet(
                wallet.copy(id = WalletId(accountIdEarlyAccess.value)),
                wallet.founder.copy(
                    accountId = accountIdEarlyAccess,
                ),
            )

            val response =
                client.toBlocking().exchange(request, BillTO::class.java)

            response.status shouldBe HttpStatus.OK
        }
    }
}