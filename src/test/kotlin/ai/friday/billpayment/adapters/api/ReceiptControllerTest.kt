package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.api.builders.BillTOBuilder
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.GetReceiptErrors
import ai.friday.billpayment.app.payment.ReceiptFiles
import ai.friday.billpayment.app.payment.ReceiptService
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.integration.CNPJ_1
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test

class ReceiptControllerTest {
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    val authentication: Authentication = Authentication.build(
        wallet.founder.accountId.value,
        listOf(Role.OWNER.name),
        mapOf<String, Any>(AUTHENTICATION_ATTRIBUTE_WALLET to wallet),
    )

    private val receiptService = mockk<ReceiptService>()

    private val receiptController = ReceiptController(
        receiptService = receiptService,
        billTOBuilder = BillTOBuilder(pixQrCodeCompoundDataBuilder = null),
    )

    @Test
    fun `deve retornar BillIsNotPaid`() {
        every { receiptService.getReceipt(any(), any(), any()) } returns GetReceiptErrors.BillIsNotPaid.left()

        val receipt =
            receiptController.getReceiptFiles(billId = billAdded.billId.value, authentication = authentication)

        receipt.status shouldBe HttpStatus.BAD_REQUEST
        receipt.body.get() shouldBe ResponseTO(code = "4001", message = "Cannot get receipt cause bill was not paid")
    }

    @Test
    fun `deve retornar ReceiptNotReady`() {
        every { receiptService.getReceipt(any(), any(), any()) } returns GetReceiptErrors.ReceiptNotReady.left()

        val receipt =
            receiptController.getReceiptFiles(billId = billAdded.billId.value, authentication = authentication)

        receipt.status shouldBe HttpStatus.BAD_REQUEST
        receipt.body.get() shouldBe ResponseTO(code = "4002", message = "Cannot get receipt cause receipt is not ready")
    }

    @Test
    fun `deve retornar BillNotFound`() {
        every { receiptService.getReceipt(any(), any(), any()) } returns GetReceiptErrors.BillNotFound.left()

        val receipt =
            receiptController.getReceiptFiles(billId = billAdded.billId.value, authentication = authentication)

        receipt.status shouldBe HttpStatus.NOT_FOUND
        receipt.body.get() shouldBe ResponseTO(code = "4004", message = "Cannot get receipt cause bill was not found")
    }

    @Test
    fun `deve retornar MemberNotAllowed`() {
        every { receiptService.getReceipt(any(), any(), any()) } returns GetReceiptErrors.MemberNotAllowed.left()

        val receipt =
            receiptController.getReceiptFiles(billId = billAdded.billId.value, authentication = authentication)

        receipt.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `deve retornar ServerError`() {
        every {
            receiptService.getReceipt(
                any(),
                any(),
                any(),
            )
        } returns GetReceiptErrors.ServerError(Exception("server error")).left()

        val receipt =
            receiptController.getReceiptFiles(billId = billAdded.billId.value, authentication = authentication)

        receipt.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        receipt.body.get() shouldBe ResponseTO(code = "5001", message = "Server Error. Try again later.")
    }

    @Test
    fun `deve retornar o recibo corretamente`() {
        every {
            receiptService.getReceipt(
                any(),
                any(),
                any(),
            )
        } returns boletoReceiptData.right()
        every {
            receiptService.resolveReceiptFiles(any())
        } returns ReceiptFiles(imageUrl = "imageUrl", pdfUrl = "pdfUrl").right()

        val receipt =
            receiptController.getReceiptFiles(billId = billAdded.billId.value, authentication = authentication)

        receipt.status shouldBe HttpStatus.OK
        receipt.body.get() shouldBe ReceiptFilesTO(imageUrl = "imageUrl", pdfUrl = "pdfUrl")
    }

    companion object {
        val boletoReceiptData = BoletoReceiptData(
            authentication = "***********.***********.88.99.AA.BB.CC.DD.EE.FF",
            dateTime = getZonedDateTime(),
            recipient = Recipient(
                name = "receipient",
                document = CNPJ_1,
                bankAccount = null,
                pixKeyDetails = null,
            ),
            totalAmount = 1042799,
            billId = BillId("FICHA_RECEIPT"),
            walletId = WalletId(WALLET_ID),
            payer = BillPayer(document = DOCUMENT, name = "receipient", alias = null),
            dueDate = getLocalDate(),
            assignor = "receipient",
            barcode = BarCode.ofDigitable("34191090080116587030201035380003586090000311015"),
            source = ActionSource.Webapp(role = Role.OWNER),
            walletName = null,
            scheduledBy = null,
            paymentPartnerName = "BANCO 756 / AGENCIA 3038",
            transactionId = TransactionId("transactionId"),
        )
    }
}