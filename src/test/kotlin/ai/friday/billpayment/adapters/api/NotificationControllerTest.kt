package ai.friday.billpayment.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.WalletConfiguration
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.buildCookie
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoicePaid
import ai.friday.billpayment.invoicePaymentScheduled
import ai.friday.billpayment.invoicePostponedByInsufficientFunds
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class NotificationControllerTest(
    embeddedServer: EmbeddedServer,
    val configuration: WalletConfiguration,
    val dynamoDB: AmazonDynamoDB,
) {

    private val dynamoClient = getDynamoDB()
    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(dynamoClient),
        refundedClient = RefundedBillDynamoDAO(dynamoClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(dynamoClient),
    )

    @MockBean(WalletRepository::class)
    fun walletRepository(): WalletRepository = walletRepository

    private val walletDAO = WalletDynamoDAO(dynamoClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(dynamoClient)
    private val inviteDAO = InviteDynamoDAO(dynamoClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(dynamoClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(dynamoClient)

    private val walletRepository: WalletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.ultraLimitedParticipant))

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `should return forbidden when user has no wallets`() {
        val request = buildRequest(buildCookie(walletFixture.cantPayParticipantAccount))
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                request,
                Argument.listOf(BillsNotificationTO::class.java),
                Argument.of(ResponseTO::class.java),
            )
        }
        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return no bills overdue when user has no bills overdue`() {
        walletRepository.save(wallet)
        saveBill(walletId = wallet.id, isExpired = false, sourceAccountId = wallet.founder.accountId)
        val invoiceAddedOverdue = invoiceAdded.copy(
            walletId = wallet.id,
            effectiveDueDate = getLocalDate().minusDays(1),
            actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
        )
        billRepository.save(Bill.build(invoiceAddedOverdue, invoicePaid))
        val request = buildRequest(buildCookie(walletFixture.founderAccount))
        val response = client.toBlocking()
            .retrieve(request, Argument.listOf(BillsNotificationTO::class.java), Argument.of(ResponseTO::class.java))
        response shouldContainExactlyInAnyOrder listOf(
            BillsNotificationTO(
                walletId = wallet.id.value,
                billsOverdue = 0,
                billsWaitingFunds = false,
            ),
        )
    }

    @Test
    fun `should return all bill overdue when user has VIEW_ALL_BILLS permission`() {
        walletRepository.save(wallet)
        saveBill(walletId = wallet.id, isExpired = true, sourceAccountId = wallet.founder.accountId)
        saveBill(walletId = wallet.id, isExpired = true, sourceAccountId = wallet.founder.accountId, waitingApproval = true)
        saveBill(
            walletId = wallet.id,
            isExpired = true,
            sourceAccountId = walletFixture.ultraLimitedParticipantAccount.accountId,
        )
        val request = buildRequest(buildCookie(walletFixture.founderAccount))
        val response = client.toBlocking()
            .retrieve(request, Argument.listOf(BillsNotificationTO::class.java), Argument.of(ResponseTO::class.java))
        response shouldContainExactlyInAnyOrder listOf(
            BillsNotificationTO(
                walletId = wallet.id.value,
                billsOverdue = 3,
                billsWaitingFunds = false,
            ),
        )
    }

    @Test
    fun `should return bills overdue on user with multiple wallets and mixed permissions`() {
        val primaryWallet =
            walletRepository.save(walletFixture.buildPrimaryWallet(walletFixture.ultraLimitedParticipantAccount))
        saveBill(walletId = primaryWallet.id, isExpired = true, sourceAccountId = primaryWallet.founder.accountId)
        saveBill(walletId = primaryWallet.id, isExpired = true, sourceAccountId = primaryWallet.founder.accountId)
        walletRepository.save(wallet)
        saveBill(walletId = wallet.id, isExpired = true, sourceAccountId = wallet.founder.accountId)
        saveBill(
            walletId = wallet.id,
            isExpired = true,
            sourceAccountId = walletFixture.ultraLimitedParticipantAccount.accountId,
        )
        saveBill(
            walletId = wallet.id,
            isExpired = true,
            sourceAccountId = walletFixture.ultraLimitedParticipantAccount.accountId,
            waitingApproval = true,
        )
        val request = buildRequest(buildCookie(walletFixture.ultraLimitedParticipantAccount))
        val response = client.toBlocking()
            .retrieve(request, Argument.listOf(BillsNotificationTO::class.java), Argument.of(ResponseTO::class.java))
        response shouldContainExactlyInAnyOrder listOf(
            BillsNotificationTO(
                walletId = wallet.id.value,
                billsOverdue = 2,
                billsWaitingFunds = false,
            ),
            BillsNotificationTO(walletId = primaryWallet.id.value, billsOverdue = 2, billsWaitingFunds = false),
        )
    }

    @Test
    fun `should return bills waiting funds on user on wallet with bill waiting funds and VIEW_ALL_BILLS permission`() {
        walletRepository.save(wallet)
        val invoiceAddedOverdue = invoiceAdded.copy(
            walletId = wallet.id,
            effectiveDueDate = getLocalDate().plusDays(1),
            actionSource = ActionSource.Api(accountId = walletFixture.ultraLimitedParticipantAccount.accountId),
        )
        billRepository.save(
            Bill.build(
                invoiceAddedOverdue,
                invoicePaymentScheduled,
                invoicePostponedByInsufficientFunds,
            ),
        )
        val request = buildRequest(buildCookie(walletFixture.founderAccount))
        val response = client.toBlocking()
            .retrieve(request, Argument.listOf(BillsNotificationTO::class.java), Argument.of(ResponseTO::class.java))
        response shouldContainExactlyInAnyOrder listOf(
            BillsNotificationTO(
                walletId = wallet.id.value,
                billsOverdue = 0,
                billsWaitingFunds = true,
            ),
        )
    }

    @Test
    fun `should not return bills waiting funds on user on wallet with bill waiting funds but ONLY_BILLS_ADDED_BY_USER permission`() {
        walletRepository.save(wallet)
        val invoiceAddedOverdue = invoiceAdded.copy(
            walletId = wallet.id,
            effectiveDueDate = getLocalDate().plusDays(1),
            actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
        )
        billRepository.save(
            Bill.build(
                invoiceAddedOverdue,
                invoicePaymentScheduled,
                invoicePostponedByInsufficientFunds,
            ),
        )
        val request = buildRequest(buildCookie(walletFixture.ultraLimitedParticipantAccount))
        val response = client.toBlocking()
            .retrieve(request, Argument.listOf(BillsNotificationTO::class.java), Argument.of(ResponseTO::class.java))
        response shouldContainExactlyInAnyOrder listOf(
            BillsNotificationTO(
                walletId = wallet.id.value,
                billsOverdue = 0,
                billsWaitingFunds = false,
            ),
        )
    }

    @Test
    fun `should return bills waiting funds on user on wallet with bill waiting funds and ONLY_BILLS_ADDED_BY_USER permission`() {
        walletRepository.save(wallet)
        val invoiceAddedOverdue = invoiceAdded.copy(
            walletId = wallet.id,
            effectiveDueDate = getLocalDate().plusDays(1),
            actionSource = ActionSource.Api(accountId = walletFixture.ultraLimitedParticipantAccount.accountId),
        )
        billRepository.save(
            Bill.build(
                invoiceAddedOverdue,
                invoicePaymentScheduled,
                invoicePostponedByInsufficientFunds,
            ),
        )
        val request = buildRequest(buildCookie(walletFixture.ultraLimitedParticipantAccount))
        val response = client.toBlocking()
            .retrieve(request, Argument.listOf(BillsNotificationTO::class.java), Argument.of(ResponseTO::class.java))
        response shouldContainExactlyInAnyOrder listOf(
            BillsNotificationTO(
                walletId = wallet.id.value,
                billsOverdue = 0,
                billsWaitingFunds = true,
            ),
        )
    }

    private fun saveBill(walletId: WalletId, isExpired: Boolean, sourceAccountId: AccountId, waitingApproval: Boolean = false) {
        val billAdded = billAdded.copy(
            billId = BillId("BILL-${UUID.randomUUID()}"),
            walletId = walletId,
            effectiveDueDate = getLocalDate().minusDays(if (isExpired) 2 else 0),
            actionSource = ActionSource.Api(accountId = sourceAccountId),
            securityValidationResult = listOf("FAKE VALIDATION"),
        )
        billRepository.save(Bill.build(billAdded))
    }

    private fun buildRequest(cookie: Cookie): MutableHttpRequest<*> {
        return HttpRequest.GET<BillsNotificationTO>("/notification/bills")
            .cookie(cookie).header("X-API-VERSION", "2")
    }
}