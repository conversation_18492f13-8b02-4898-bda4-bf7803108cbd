package ai.friday.billpayment.adapters.api

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.TokenDbRepository
import ai.friday.billpayment.adapters.dynamodb.TokenDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.InvalidTokenException
import ai.friday.billpayment.app.account.TokenKey
import ai.friday.billpayment.app.account.TokenOnboardingConfiguration
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.TokenData
import ai.friday.billpayment.app.integrations.TokenDataWithExpiration
import ai.friday.billpayment.app.integrations.TokenRepository
import ai.friday.billpayment.app.integrations.TokenType
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessMatchVerify
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.LocalDbCreationRule.Companion.dynamoDB
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import java.time.Duration
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class PasswordResetControllerIntegrationTest(
    embeddedServer: EmbeddedServer,
    private val configuration: TokenOnboardingConfiguration,
) {
    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)
    private val dynamoClient = DynamoDBUtils.setupDynamoDB()
    private val tokenDynamoDAO = TokenDynamoDAO(dynamoClient)

    private val updatedPassword = "updatedPassword"
    private val now = getZonedDateTime()

    @MockBean(NotificationAdapter::class)
    fun getNotificationAdapter() = notificationAdapter
    private val notificationAdapter = mockk<NotificationAdapter>() {
        every {
            notifyForgotPasswordToken(any(), any(), any(), any(), any())
        } just Runs
    }

    @MockBean(TokenRepository::class)
    fun getTokenRepository() = tokenRepository
    private val tokenRepository = TokenDbRepository(client = tokenDynamoDAO)

    @MockBean(LivenessService::class)
    fun getLivenessService() = livenessService
    private val livenessService: LivenessService = mockk() {
        every {
            hasCompletedEnrollment(any())
        } returns true.right()
    }

    @MockBean(UserPoolAdapter::class)
    fun getUserPoolAdapter() = userPoolAdapter
    private val userPoolAdapter: UserPoolAdapter = mockk() {
        every {
            doesUserExist(any())
        } returns true
    }

    @BeforeEach
    fun init() {
        loadAccountIntoDb(amazonDynamoDB = dynamoDB, email = ACCOUNT.emailAddress.value)
    }

    @Nested
    @DisplayName("ForgotPassword")
    inner class ForgotPassword {

        @Test
        fun `deve retornar OK e enviar o token OTP por email quando o usuário existir e tiver feito enrollment com liveness`() {
            val request = buildForgotRequest()

            val response = withGivenDateTime(now) {
                client.toBlocking().exchange(request, Argument.STRING, Argument.STRING)
            }

            response.status shouldBe HttpStatus.NO_CONTENT

            verify {
                notificationAdapter.notifyForgotPasswordToken(
                    accountId = ACCOUNT.accountId,
                    emailAddress = ACCOUNT.emailAddress,
                    document = ACCOUNT.document,
                    token = any(),
                    duration = any(),
                )
            }

            val foundToken = tokenRepository.retrieveNotExpired(
                ACCOUNT.accountId,
                TokenType.EMAIL,
            )
            foundToken.isRight().shouldBeTrue()
            foundToken.map {
                it.value shouldBe ACCOUNT.emailAddress.value
                it.type shouldBe TokenType.EMAIL
                it.expiration shouldBe now.plus(Duration.ofSeconds(configuration.passwordRecoveryDuration))
                    .toEpochSecond()
            }
        }

        @Test
        fun `deve retornar OK e não enviar o token OTP por email quando o usuário não existir`() {
            val request = buildForgotRequest(DOCUMENT_2)
            val now = getZonedDateTime()

            val response = withGivenDateTime(now) {
                client.toBlocking().exchange(request, Argument.STRING, Argument.STRING)
            }

            response.status shouldBe HttpStatus.NO_CONTENT

            verify {
                notificationAdapter wasNot Called
            }

            val invalidToken = tokenRepository.retrieveNotExpired(ACCOUNT.accountId, TokenType.EMAIL)
            invalidToken.isLeft() shouldBe true
            invalidToken.mapLeft { it.shouldBeTypeOf<InvalidTokenException>() }
        }

        @Test
        fun `deve retornar CONFLICT se o usuário existir e não tiver o liveness (fluxo antigo utilizando o suporte)`() {
            every {
                livenessService.hasCompletedEnrollment(any())
            } returns false.right()

            val request = buildForgotRequest(DOCUMENT)
            val now = getZonedDateTime()

            val response = assertThrows<HttpClientResponseException> {
                withGivenDateTime(now) {
                    client.toBlocking().exchange(request, Argument.STRING, Argument.of(ResponseTO::class.java))
                }
            }

            response.status shouldBe HttpStatus.CONFLICT
            response.response.getBody(Argument.of(ResponseTO::class.java)).get().code shouldBe "4091"

            verify {
                notificationAdapter wasNot Called
            }

            val invalidToken = tokenRepository.retrieveNotExpired(ACCOUNT.accountId, TokenType.EMAIL)
            invalidToken.isLeft() shouldBe true
            invalidToken.mapLeft { it.shouldBeTypeOf<InvalidTokenException>() }
        }

        @Test
        fun `deve retornar CONFLICT se o usuário existir e não tiver senha (fluxo antigo utilizando o suporte)`() {
            every {
                userPoolAdapter.doesUserExist(any())
            } returns false

            val request = buildForgotRequest(DOCUMENT)
            val now = getZonedDateTime()

            val response = assertThrows<HttpClientResponseException> {
                withGivenDateTime(now) {
                    client.toBlocking().exchange(request, Argument.STRING, Argument.of(ResponseTO::class.java))
                }
            }

            response.status shouldBe HttpStatus.CONFLICT
            response.response.getBody(Argument.of(ResponseTO::class.java)).get().code shouldBe "4092"

            verify {
                notificationAdapter wasNot Called
            }

            val invalidToken = tokenRepository.retrieveNotExpired(ACCOUNT.accountId, TokenType.EMAIL)
            invalidToken.isLeft() shouldBe true
            invalidToken.mapLeft { it.shouldBeTypeOf<InvalidTokenException>() }
        }
    }

    @Nested
    @DisplayName("VerifyOtp")
    inner class VerifyOtp {
        private val validToken = "123456"
        private val now = getZonedDateTime()

        @BeforeEach
        fun init() {
            tokenRepository.save(
                tokenKey = TokenKey(
                    accountId = AccountId(ACCOUNT_ID),
                    value = validToken,
                ),
                tokenData = TokenDataWithExpiration(
                    TokenData.of(ACCOUNT.emailAddress),
                    expiration = now.plusMinutes(50).toEpochSecond(),
                ),
            )
        }

        @Test
        fun `deve retornar CONFLICT se o token estiver errado`() {
            val request = buildVerifyRequest("INVALID_TOKEN")

            val response = assertThrows<HttpClientResponseException> {
                withGivenDateTime(now) {
                    client.toBlocking().exchange(request, Argument.STRING, Argument.of(ResponseTO::class.java))
                }
            }

            response.status shouldBe HttpStatus.CONFLICT
            response.response.getBody(Argument.of(ResponseTO::class.java)).get().code shouldBe "4094"

            verify {
                livenessService wasNot Called
            }
        }

        @Test
        fun `deve retornar INTERNAL_SERVER_ERROR se o usuário não puder fazer o match`() {
            every {
                livenessService.match(any())
            } returns LivenessErrors.MatchUnavailable.left()

            val request = buildVerifyRequest(validToken)

            val response = assertThrows<HttpClientResponseException> {
                withGivenDateTime(now) {
                    client.toBlocking().exchange(request, Argument.STRING, Argument.of(ResponseTO::class.java))
                }
            }

            response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        }

        @Test
        fun `deve retornar o liveness id se o token for valido e der o match`() {
            val livenessId = LivenessId("LivenessId_FAKE")

            every {
                livenessService.match(any())
            } returns livenessId.right()

            val request = buildVerifyRequest(validToken)

            val response = withGivenDateTime(now) {
                client.toBlocking().exchange(
                    request,
                    Argument.of(LivenessValidatedOTPResponseTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )
            }

            response.status shouldBe HttpStatus.OK
            response.body()!!.livenessId shouldBe livenessId.value

            val livenessToken = tokenRepository.retrieveNotExpired(
                ACCOUNT.accountId,
                TokenType.LIVENESS_ID,
            )

            val token = livenessToken
            token.isRight() shouldBe true
            token.map { tokenData ->
                tokenData.value shouldBe livenessId.value
            }
        }
    }

    @Nested
    @DisplayName("ResetPassword")
    inner class ResetPassword {

        private val livenessId = LivenessId("LivenessId_FAKE")

        @Test
        fun `deve retornar CONFLICT se o match não passar e não alterar a senha do usuário`() {
            every {
                livenessService.verifyMatch(any())
            } returns LivenessMatchVerify(
                livenessId = livenessId,
                accountId = AccountId(ACCOUNT_ID),
                match = false,
                attempt = 1,
            ).right()

            val request = buildResetRequest(livenessId)

            val response = assertThrows<HttpClientResponseException> {
                withGivenDateTime(now) {
                    client.toBlocking().exchange(
                        request,
                        Argument.of(LivenessValidatedOTPResponseTO::class.java),
                        Argument.of(ResponseTO::class.java),
                    )
                }
            }

            response.status shouldBe HttpStatus.CONFLICT
            verify {
                userPoolAdapter wasNot Called
            }

            val livenessToken = tokenRepository.retrieveNotExpired(
                ACCOUNT.accountId,
                TokenType.LIVENESS_ID,
            )
            livenessToken.isLeft() shouldBe true
            livenessToken.mapLeft { it.shouldBeTypeOf<InvalidTokenException>() }
        }

        @Test
        fun `deve retornar INTERNAL_SERVER_ERROR se o match não passar e não alterar a senha do usuário`() {
            every {
                livenessService.verifyMatch(any())
            } returns LivenessErrors.MatchUnavailable.left()

            val request = buildResetRequest(livenessId)

            val response = assertThrows<HttpClientResponseException> {
                withGivenDateTime(now) {
                    client.toBlocking().exchange(
                        request,
                        Argument.of(LivenessValidatedOTPResponseTO::class.java),
                        Argument.of(ResponseTO::class.java),
                    )
                }
            }

            response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
            verify {
                userPoolAdapter wasNot Called
            }

            val livenessToken = tokenRepository.retrieveNotExpired(
                ACCOUNT.accountId,
                TokenType.LIVENESS_ID,
            )
            livenessToken.isLeft() shouldBe true
            livenessToken.mapLeft { it.shouldBeTypeOf<InvalidTokenException>() }
        }

        @Test
        fun `deve alterar a senha do usuário se o match passar`() {
            every {
                userPoolAdapter.setUserPassword(ACCOUNT.document, updatedPassword)
            } just Runs

            tokenRepository.save(
                tokenKey = TokenKey(
                    accountId = ACCOUNT.accountId,
                    value = livenessId.value,
                ),
                tokenData = TokenDataWithExpiration(
                    TokenData.of(livenessId),
                    expiration = now.plusMinutes(50).toEpochSecond(),
                ),
            )

            every {
                livenessService.verifyMatch(any())
            } returns LivenessMatchVerify(
                livenessId = livenessId,
                accountId = AccountId(ACCOUNT_ID),
                match = true,
                attempt = 1,
            ).right()

            val request = buildResetRequest(livenessId)

            val response =
                withGivenDateTime(now) {
                    client.toBlocking().exchange(
                        request,
                        Argument.of(LivenessValidatedOTPResponseTO::class.java),
                        Argument.of(ResponseTO::class.java),
                    )
                }

            response.status shouldBe HttpStatus.NO_CONTENT
            verify {
                userPoolAdapter.setUserPassword(ACCOUNT.document, updatedPassword)
            }

            val livenessToken = tokenRepository.retrieveNotExpired(
                ACCOUNT.accountId,
                TokenType.LIVENESS_ID,
            )
            livenessToken.isLeft() shouldBe true
            livenessToken.mapLeft { it.shouldBeTypeOf<InvalidTokenException>() }
        }
    }

    private fun buildForgotRequest(document: String = DOCUMENT): MutableHttpRequest<ForgotPasswordRequestTO> {
        return HttpRequest.PUT("/password/liveness/forgot", ForgotPasswordRequestTO(document))
            .header("X-API-VERSION", "2")
    }

    private fun buildVerifyRequest(otp: String): MutableHttpRequest<VerifyOTPRequestTO> {
        return HttpRequest.POST(
            "/password/liveness/verify",
            VerifyOTPRequestTO(otp = otp, document = ACCOUNT.document),
        ).header("X-API-VERSION", "2")
    }

    private fun buildResetRequest(livenessId: LivenessId): MutableHttpRequest<UpdatePasswordRequestTO> {
        return HttpRequest.POST(
            "/password/liveness/reset",
            UpdatePasswordRequestTO(livenessId = livenessId.value, password = updatedPassword),
        ).header("X-API-VERSION", "2")
    }
}