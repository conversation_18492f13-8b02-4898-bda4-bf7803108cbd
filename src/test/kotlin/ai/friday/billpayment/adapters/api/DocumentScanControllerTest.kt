package ai.friday.billpayment.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.accountRegisterDataWithAddress
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.DocumentScan
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.documentscan.DocumentScanId
import ai.friday.billpayment.app.documentscan.DocumentScanRepository
import ai.friday.billpayment.app.documentscan.DocumentScanService
import ai.friday.billpayment.app.documentscan.UpsertDocumentScanIdError
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.msisdnauth.TEMPORARY_NICKNAME
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DocumentScanControllerTest {
    private val documentScanRepositoryMock: DocumentScanRepository = mockk()
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbEnhancedClient = getDynamoDB()
    private val accountRegisterDAO = AccountRegisterDynamoDAO(dynamoDbEnhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(dynamoDbEnhancedClient)
    private val registerRepository: AccountRegisterRepository = AccountRegisterDbRepository(
        accountRegisterDAO = accountRegisterDAO,
        originalOcrAndPersonDataDAO = originalOcrAndPersonDataDAO,
        objectRepository = mockk(relaxUnitFun = true),
    )

    private val accountDAO = AccountDynamoDAO(dynamoDbEnhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoDbEnhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoDbEnhancedClient)
    private val nsuDAO = NSUDynamoDAO(dynamoDbEnhancedClient)
    private val transactionDynamo = TransactionDynamo(dynamoDbEnhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val documentScanService = DocumentScanService(
        documentScanRepository = documentScanRepositoryMock,
        registerRepository = registerRepository,
        accountRepository = accountRepository,
    )
    private val documentScanController = DocumentScanController(documentScanService)

    private val guestAccountId = AccountId(ACCOUNT_ID)
    private val ownerAccountId = AccountId(ACCOUNT_ID_2)
    private val expectedDocumentScanId = DocumentScanId("any")
    private val auth = mockk<Authentication> {
        every { name } returns guestAccountId.value
        every { roles } returns listOf(Role.GUEST.name)
        every { getAttributes() } returns mapOf("role" to Role.GUEST.name)
    }

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)

        accountRepository.create(
            username = TEMPORARY_NICKNAME,
            emailAddress = EmailAddress("<EMAIL>"),
            accountId = AccountId(ACCOUNT_ID),
            registrationType = RegistrationType.UPGRADED,
        )
        registerRepository.save(
            accountRegisterDataWithAddress.copy(
                accountId = AccountId(ACCOUNT_ID),
                registrationType = RegistrationType.UPGRADED,
            ),
        )

        every {
            documentScanRepositoryMock.upsertDocumentScanId(any())
        } returns expectedDocumentScanId.right()
    }

    @Test
    fun `deve criar um document scan id e salvar no register do usuário upgraded`() {
        val auth = mockk<Authentication> {
            every { name } returns ownerAccountId.value
            every { roles } returns listOf(Role.OWNER.name)
            every { getAttributes() } returns mapOf("role" to Role.OWNER.name)
        }

        accountRepository.save(ACCOUNT.copy(accountId = ownerAccountId, type = UserAccountType.BASIC_ACCOUNT))

        registerRepository.save(
            accountRegisterCompleted.copy(
                accountId = ownerAccountId,
                registrationType = RegistrationType.BASIC,
            ),
        )

        val httpResponse = documentScanController.postDocumentScan(authentication = auth, request = PostDocumentScanRequestTO("CNH"))

        httpResponse.body() shouldBe mapOf("documentScanId" to expectedDocumentScanId.value)

        httpResponse.status shouldBe HttpStatus.CREATED

        verify {
            documentScanRepositoryMock.upsertDocumentScanId(ownerAccountId)
        }

        with(registerRepository.findByAccountId(ownerAccountId)) {
            documentScan shouldBe DocumentScan(
                documentScanId = expectedDocumentScanId,
                documentType = DocumentType.CNH,
            )
        }
    }

    @Test
    fun `deve criar um document scan id e salvar no register do usuário full`() {
        documentScanController.postDocumentScan(authentication = auth, request = PostDocumentScanRequestTO("CNH"))
            .status shouldBe HttpStatus.CREATED

        verify {
            documentScanRepositoryMock.upsertDocumentScanId(guestAccountId)
        }

        with(registerRepository.findByAccountId(guestAccountId)) {
            documentScan shouldBe DocumentScan(
                documentScanId = expectedDocumentScanId,
                documentType = DocumentType.CNH,
            )
        }
    }

    @Test
    fun `se o usuário tiver a conta fechada, deve retonar erro`() {
        val auth = mockk<Authentication> {
            every { name } returns ownerAccountId.value
            every { roles } returns listOf(Role.OWNER.name)
            every { getAttributes() } returns mapOf("role" to Role.OWNER.name)
        }

        accountRepository.save(ACCOUNT.copy(accountId = ownerAccountId, status = AccountStatus.CLOSED))

        documentScanController.postDocumentScan(authentication = auth, request = PostDocumentScanRequestTO("CNH"))
            .status shouldBe HttpStatus.CONFLICT

        verify(exactly = 0) {
            documentScanRepositoryMock.upsertDocumentScanId(ownerAccountId)
        }
    }

    @Test
    fun `se o usuário tiver a conta negada, deve retornar erro`() {
        accountRepository.updatePartialAccountStatus(guestAccountId, AccountStatus.DENIED)

        documentScanController.postDocumentScan(authentication = auth, request = PostDocumentScanRequestTO("CNH"))
            .status shouldBe HttpStatus.CONFLICT

        verify(exactly = 0) {
            documentScanRepositoryMock.upsertDocumentScanId(guestAccountId)
        }
    }

    @Test
    fun `se o der um erro generico, deve retornar internal server error`() {
        every {
            documentScanRepositoryMock.upsertDocumentScanId(guestAccountId)
        } returns UpsertDocumentScanIdError.Unexpected(NoStackTraceException()).left()

        documentScanController.postDocumentScan(authentication = auth, request = PostDocumentScanRequestTO("CNH"))
            .status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
    }
}