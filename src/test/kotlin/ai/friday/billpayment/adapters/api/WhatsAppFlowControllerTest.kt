package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.uuid
import ai.friday.billpayment.app.whatsapp.SendFlowResult
import ai.friday.billpayment.app.whatsapp.WhatsAppService
import ai.friday.billpayment.integration.CookieFixture.withBackofficeCookie
import ai.friday.billpayment.integration.blockingHttpClient
import com.fasterxml.jackson.databind.ObjectMapper
import io.kotest.matchers.collections.shouldContainAll
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.client.BlockingHttpClient
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import jakarta.inject.Inject
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV])
class WhatsAppFlowControllerTest(server: EmbeddedServer) {
    @Inject
    lateinit var objectMapper: ObjectMapper

    private val client: BlockingHttpClient = server.blockingHttpClient()
    private val whatsAppService = mockk<WhatsAppService>(relaxed = true)

    @MockBean(WhatsAppService::class)
    fun whatsAppService(): WhatsAppService = whatsAppService

    @Test
    fun `should request flow with success when receive accountIds`() {
        val accounts = listOf("account1", "account2")

        val requestPayload = FlowMessageRequestTO(templateName = "template1", accounts = accounts)
        val requestBody = objectMapper.writeValueAsString(requestPayload)

        every {
            whatsAppService.sendFlow(any())
        } answers {
            val successes = accounts.map { accountId ->
                SendFlowResult.Item(
                    phoneNumber = "*************",
                    accountId = accountId,
                    messageId = uuid(),
                )
            }

            Result.success(SendFlowResult(successes = successes))
        }

        val request: HttpRequest<String> = HttpRequest.POST("/whatsappFlow/messages", requestBody)
            .withBackofficeCookie()
            .contentType("application/json")
            .accept("application/json")

        val response: HttpResponse<SendFlowResult> = client.exchange(request, SendFlowResult::class.java)

        response.body()
            ?.successes
            ?.map { it.accountId }
            ?.shouldContainAll(accounts)

        response.status.code shouldBe 200
    }
}