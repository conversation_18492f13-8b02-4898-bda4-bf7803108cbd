package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.auth.FROM_CLAIM_EMAIL
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountEvent
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.account.LegacyAccountConfiguration
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.LoginRepository
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.integrations.UserPoolPasswordValidationException
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.msisdnauth.createTemporaryEmail
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.NAME
import ai.friday.billpayment.integration.SecurityFixture
import ai.friday.billpayment.integration.asJWT
import ai.friday.billpayment.mobilePhone
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class SimpleSignupControllerTest(embeddedServer: EmbeddedServer, val configuration: MsisdnAuthConfiguration) {
    private val securityFixture = SecurityFixture()

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(AccountRepository::class)
    fun accountRepository() = accountRepository
    private val accountRepository: AccountRepository = mockk()

    @MockBean(UserPoolAdapter::class)
    fun userPoolAdapter() = userPoolAdapter
    private val userPoolAdapter: UserPoolAdapter = mockk()

    @MockBean(EventPublisher::class)
    fun eventPublisher() = eventPublisher
    private val eventPublisher: EventPublisher = mockk()

    @MockBean(LoginRepository::class)
    fun loginRepository() = loginRepository
    private val loginRepository: LoginRepository = mockk()

    val cookie = securityFixture.cookieOTPGuest(configuration = configuration)
    val account = setupAccountRepository(cookie)

    @BeforeEach
    fun setup() {
        every {
            userPoolAdapter.createUser(any(), any(), any(), any(), any(), any(), any(), any())
        } just Runs

        every { userPoolAdapter.setMfaPreference(any(), any()) } returns Unit.right()

        every {
            userPoolAdapter.setUserPassword(any(), any())
        } just Runs

        every {
            userPoolAdapter.addUserToGroup(any(), any())
        } just Runs

        every {
            userPoolAdapter.signOutUser(any())
        } just Runs

        every {
            userPoolAdapter.isUserEnabled(any())
        } returns true

        every {
            eventPublisher.publish(any<AccountEvent>())
        } just Runs
    }

    @Test
    fun `deve bloquear o acesso para a role OWNER`() {
        val requestTO = SignupRequestTO(password = "123456")

        val request = buildSignupRequest(securityFixture.cookieAuthOwner, requestTO)

        val response = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
        }

        response.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `deve criar usuario no cognito quando a role for OTP_GUEST`() {
        val requestTO = SignupRequestTO(password = "123456")
        val username = "Name"
        val providerId = createTemporaryEmail(MobilePhone("+*************")).value

        every {
            userPoolAdapter.doesUserExist(username = DOCUMENT)
        } returns false

        every {
            loginRepository.findActiveUserLoginProvider(account.emailAddress)
        } returns listOf(
            ProviderUser(
                id = providerId,
                providerName = ProviderName.MSISDN,
                username = username,
                emailAddress = account.emailAddress,
            ),
        )

        every {
            userPoolAdapter.doesUserExist(username = username)
        } returns true

        val request = buildSignupRequest(cookie, requestTO)

        val response = client.toBlocking()
            .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.CREATED
        verify {
            userPoolAdapter.createUser(
                username = DOCUMENT,
                password = requestTO.password,
                accountId = account.accountId,
                emailAddress = account.emailAddress,
                phoneNumber = MobilePhone(account.mobilePhone),
                role = Role.OWNER,
                name = account.name,
                setMFA = false,
            )
            eventPublisher.publish(any<AccountEvent>())
        }

        verify(exactly = 0) {
            userPoolAdapter.setMfaPreference(any(), any())
        }
    }

    @Test
    fun `deve retornar conflito se o usuário já existe e está ativo no cognito`() {
        val requestTO = SignupRequestTO(password = "123456")

        every {
            userPoolAdapter.doesUserExist(username = DOCUMENT)
        } returns true

        val request = buildSignupRequest(cookie, requestTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.CONFLICT
        thrown.response.getBody(ResponseTO::class.java)
            .get().code shouldBe SimpleSignupErrorResponse.USERNAME_ALREADY_EXISTS.code
        verify(exactly = 0) {
            userPoolAdapter.createUser(
                username = DOCUMENT,
                password = requestTO.password,
                accountId = account.accountId,
                emailAddress = account.emailAddress,
                phoneNumber = MobilePhone(account.mobilePhone),
                role = Role.OWNER,
                name = account.name,
                setMFA = false,
            )
            userPoolAdapter.addUserToGroup(any(), any())
            userPoolAdapter.signOutUser(any())
            eventPublisher.publish(any<AccountEvent>())
        }
    }

    @Test
    fun `deve retornar erro de senha inválida caso a senha não passe na validação do cognito`() {
        val requestTO = SignupRequestTO(password = "123456")

        every {
            userPoolAdapter.doesUserExist(username = DOCUMENT)
        } returns false

        every {
            userPoolAdapter.createUser(any(), any(), any(), any(), any(), any(), any(), any())
        } throws UserPoolPasswordValidationException("")

        val request = buildSignupRequest(cookie, requestTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java)
            .get().code shouldBe SimpleSignupErrorResponse.INVALID_PASSWORD.code
        verify(exactly = 1) {
            userPoolAdapter.createUser(
                username = DOCUMENT,
                password = requestTO.password,
                accountId = account.accountId,
                emailAddress = account.emailAddress,
                phoneNumber = MobilePhone(account.mobilePhone),
                role = Role.OWNER,
                name = account.name,
                setMFA = false,
            )
        }

        verify(exactly = 0) {
            userPoolAdapter.addUserToGroup(any(), any())
            userPoolAdapter.signOutUser(any())
            eventPublisher.publish(any<AccountEvent>())
        }
    }

    @Test
    fun `deve reativar usuario desativado já existente no cognito e atualizar a senha`() {
        every {
            userPoolAdapter.isUserEnabled(any())
        } returns false

        every {
            userPoolAdapter.enableUser(any())
        } returns Unit.right()

        every {
            userPoolAdapter.doesUserExist(username = DOCUMENT)
        } returns true

        val requestTO = SignupRequestTO(password = "123456")

        val request = buildSignupRequest(cookie, requestTO)

        val response = client.toBlocking()
            .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.CREATED

        verify {
            userPoolAdapter.setAccountId(DOCUMENT, AccountId(cookie.asJWT().jwtClaimsSet.subject))
            userPoolAdapter.enableUser(DOCUMENT)
            userPoolAdapter.setUserPassword(DOCUMENT, "123456")
        }
    }

    @Test
    fun `deve retornar erro de conflito se o documento informado for diferente do documento da pessoa autenticada`() {
        val requestTO = SignupRequestTO(password = "123456")

        every {
            userPoolAdapter.doesUserExist(username = DOCUMENT)
        } returns true

        val request = buildSignupRequest(cookie, requestTO, document = DOCUMENT_2)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.CONFLICT
        thrown.response.getBody(ResponseTO::class.java)
            .get().code shouldBe SimpleSignupErrorResponse.DOCUMENT_MISMATCH.code
        verify(exactly = 0) {
            userPoolAdapter.createUser(
                username = DOCUMENT,
                password = requestTO.password,
                accountId = account.accountId,
                emailAddress = account.emailAddress,
                phoneNumber = MobilePhone(account.mobilePhone),
                role = Role.OWNER,
                name = account.name,
                setMFA = false,
            )
            userPoolAdapter.addUserToGroup(any(), any())
            userPoolAdapter.signOutUser(any())
            eventPublisher.publish(any<AccountEvent>())
        }
    }

    @Test
    fun `deve retornar conflito se não for encontrada uma conta para o documento informado`() {
        every {
            accountRepository.findById(any())
        } throws AccountNotFoundException("")

        val requestTO = SignupRequestTO(password = "123456")

        every {
            userPoolAdapter.doesUserExist(username = DOCUMENT)
        } returns true

        val request = buildSignupRequest(cookie, requestTO, document = DOCUMENT_2)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.CONFLICT
        thrown.response.getBody(ResponseTO::class.java)
            .get().code shouldBe SimpleSignupErrorResponse.ACCOUNT_NOT_FOUND.code

        verify(exactly = 0) {
            userPoolAdapter.createUser(
                username = DOCUMENT,
                password = requestTO.password,
                accountId = account.accountId,
                emailAddress = account.emailAddress,
                phoneNumber = MobilePhone(account.mobilePhone),
                role = Role.OWNER,
                name = account.name,
                setMFA = false,
            )
            userPoolAdapter.addUserToGroup(any(), any())
            userPoolAdapter.signOutUser(any())
            eventPublisher.publish(any<AccountEvent>())
        }
    }

    private fun buildSignupRequest(
        cookie: Cookie,
        requestTO: SignupRequestTO,
        document: String = DOCUMENT,
    ): MutableHttpRequest<*> {
        return HttpRequest.PUT("/register/simpleSignUp/$document", requestTO)
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private fun setupAccountRepository(cookie: Cookie, msisdn: String? = null): Account {
        val jwt = cookie.asJWT()
        val accountId = jwt.jwtClaimsSet.subject
        val email = jwt.jwtClaimsSet.getStringClaim(FROM_CLAIM_EMAIL)

        val account = Account(
            accountId = AccountId(accountId),
            name = NAME,
            emailAddress = EmailAddress(email),
            document = DOCUMENT,
            documentType = "CPF",
            mobilePhone = msisdn ?: mobilePhone.msisdn,
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
            status = AccountStatus.ACTIVE,
            configuration = LegacyAccountConfiguration(
                creditCardConfiguration = CreditCardConfiguration(
                    quota = 0,
                ),
                defaultWalletId = null,
                receiveDDANotification = true,
                receiveNotification = true,
            ),
            imageUrlSmall = "image_url_small",
            imageUrlLarge = "image_url_large",
            subscriptionType = SubscriptionType.PIX,
        )

        every {
            accountRepository.findById(any())
        } returns account

        return account
    }
}