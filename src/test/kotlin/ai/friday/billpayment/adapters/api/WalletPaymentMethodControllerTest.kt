package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.ExternalPaymentMethod
import ai.friday.billpayment.app.account.PaymentMethod
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.integrations.CreditCardService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.usage.CreditCardRiskUsage
import ai.friday.billpayment.app.usage.CreditCardUsage
import ai.friday.billpayment.app.usage.CreditCardUsageService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.WalletFixture
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.every
import io.mockk.mockk
import java.time.ZonedDateTime
import org.junit.jupiter.api.Test

class WalletPaymentMethodControllerTest {

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.assistant))
    private val evpPixKeyFixture = PixKey(value = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa ", type = PixKeyType.EVP)

    private val creditCardUsageService: CreditCardUsageService = mockk(relaxed = true)
    private val accountService: AccountService = mockk(relaxed = true)
    private val creditCarService: CreditCardService = mockk(relaxed = true)
    private val balanceAmount = Balance(15L)
    private val balanceService: BalanceService = mockk(relaxed = true) {
        every {
            getBalanceFrom(wallet.founder.accountId, wallet.paymentMethodId)
        } returns balanceAmount
    }

    private val walletService: WalletService = mockk() {
        every {
            walletEvpPixKey(any())
        } returns evpPixKeyFixture
    }

    private val controller = WalletPaymentMethodController(
        creditCardUsageService = creditCardUsageService,
        accountService = accountService,
        creditCarService = creditCarService,
        balanceService = balanceService,
        walletService = walletService,
    ).also {
        it.pixKeyEmailDomain = "@test.com"
    }

    private val accountId = AccountId(ACCOUNT_ID)

    private val secondaryWallet = walletFixture.buildWallet(type = WalletType.SECONDARY)

    private val authentication = mockk<Authentication>() {
        every { name } returns wallet.founder.accountId.value
        every { getWallet() } returns wallet
    }

    private val internalBankAccountPaymentMethod = buildAccountPaymentMethod(buildInternalBankAccount())

    private val externalPaymentMethod = buildAccountPaymentMethod(buildExternalPaymentMethod())

    private val creditCardPaymentMethod = buildCreditCardPaymentMethod()

    private val creditCardAccountPaymentMethod = buildAccountPaymentMethod(creditCardPaymentMethod)

    @Test
    fun `deve retornar sucesso sem informações bancárias quando o metodo de pagamento for do tipo External`() {
        every {
            creditCardUsageService.calculateCreditCardUsage(any())
        } returns buildUsage()

        every {
            accountService.findAccountPaymentMethodByIdAndAccountId(any(), any())
        } returns externalPaymentMethod

        val response = controller.retrievePaymentMethods(authentication, wallet.id.value)

        response.status shouldBe HttpStatus.OK

        with(response.getBody(WalletPaymentMethodsResponseTO::class.java).get()) {
            validateUsageResponse()
            validatePixKey()
            validateEvpPixKey()
            bankDetails.shouldBeNull()
            balance.paymentMethodId shouldBe wallet.paymentMethodId.value
            balance.amount shouldBe balanceAmount.amount
        }
    }

    @Test
    fun `deve retornar sucesso com informações bancárias quando o metodo de pagamento for do tipo InternalBankAccount`() {
        every {
            creditCardUsageService.calculateCreditCardUsage(any())
        } returns buildUsage()

        every {
            accountService.findAccountPaymentMethodByIdAndAccountId(any(), any())
        } returns internalBankAccountPaymentMethod

        every {
            creditCarService.findActiveAndPendingCreditCards(any())
        } returns listOf(
            creditCardAccountPaymentMethod,
        )

        val response = controller.retrievePaymentMethods(authentication, wallet.id.value)

        response.status shouldBe HttpStatus.OK

        with(response.getBody(WalletPaymentMethodsResponseTO::class.java).get()) {
            validateUsageResponse()
            validatePixKey()
            validateEvpPixKey()
            validateBankDetailsResponse()
            this.creditCards.shouldHaveSize(1)
            with(creditCards.first()) {
                id shouldBe creditCardAccountPaymentMethod.id.value
                type shouldBe "CREDIT_CARD"
                creditCard.brand shouldBe "visa"
                creditCard.pan shouldBe "************7123"
                creditCard.status shouldBe "ACTIVE"
                creditCard.expiryDate shouldBe "10/28"
                creditCard.mainCard.shouldBeTrue()
            }
            balance.paymentMethodId shouldBe wallet.paymentMethodId.value
            balance.amount shouldBe balanceAmount.amount
        }
    }

    @Test
    fun `deve retornar erro quando o metodo de pagamento principal da carteira for do tipo CreditCard`() {
        every {
            creditCardUsageService.calculateCreditCardUsage(any())
        } returns buildUsage()

        every {
            accountService.findAccountPaymentMethodByIdAndAccountId(any(), any())
        } returns creditCardAccountPaymentMethod

        val response = controller.retrievePaymentMethods(authentication, wallet.id.value)

        response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
    }

    @Test
    fun `deve retornar sucesso com chave pix da carteira secundaria quando a carteira informada nao for a carteira principal do usuario`() {
        every {
            creditCardUsageService.calculateCreditCardUsage(any())
        } returns buildUsage()

        every {
            accountService.findAccountPaymentMethodByIdAndAccountId(any(), any())
        } returns internalBankAccountPaymentMethod

        every { authentication.getWallet() } returns secondaryWallet

        val response = controller.retrievePaymentMethods(authentication, wallet.id.value)

        response.status shouldBe HttpStatus.OK

        with(response.getBody(WalletPaymentMethodsResponseTO::class.java).get()) {
            validateUsageResponse()
            validatePixKey("carteira.***********@@test.com")
            validateEvpPixKey()
            validateBankDetailsResponse()
            balance.paymentMethodId shouldBe wallet.paymentMethodId.value
            balance.amount shouldBe balanceAmount.amount
        }
    }

    @Test
    fun `deve retornar saldo null quando usuário não tiver permissão para ver o saldo da carteira`() {
        every {
            authentication.name
        } returns walletFixture.assistant.accountId.value

        every {
            creditCardUsageService.calculateCreditCardUsage(any())
        } returns buildUsage()

        every {
            accountService.findAccountPaymentMethodByIdAndAccountId(any(), any())
        } returns internalBankAccountPaymentMethod

        val response = controller.retrievePaymentMethods(authentication, wallet.id.value)

        response.status shouldBe HttpStatus.OK

        with(response.getBody(WalletPaymentMethodsResponseTO::class.java).get()) {
            validateUsageResponse()
            validatePixKey()
            validateEvpPixKey()
            validateBankDetailsResponse()
            balance.paymentMethodId shouldBe wallet.paymentMethodId.value
            balance.amount.shouldBeNull()
        }
    }

    private fun WalletPaymentMethodsResponseTO.validateBankDetailsResponse() {
        with(this.bankDetails!!) {
            this.name shouldBe wallet.founder.name
            this.document shouldBe wallet.founder.document
            this.bankNo shouldBe 4104
            this.routingNo shouldBe 4714
            this.accountNo shouldBe 2213
            this.accountDv shouldBe "vero"
        }
    }

    private fun WalletPaymentMethodsResponseTO.validateUsageResponse() {
        this.usage.usage shouldBe 1000
        this.usage.quota shouldBe 2000
        this.usage.cashInFee shouldBe 400
    }

    private fun buildUsage() = CreditCardUsage(
        usage = 1000,
        quota = 2000,
        cashInFee = 400,
        riskUsages = mapOf(
            RiskLevel.LOW to CreditCardRiskUsage(1500L, 500L),
        ),
    )

    private fun buildAccountPaymentMethod(paymentMethod: PaymentMethod) = AccountPaymentMethod(
        id = AccountPaymentMethodId(value = "123"),
        accountId = accountId,
        method = paymentMethod,
        status = AccountPaymentMethodStatus.ACTIVE,
        created = ZonedDateTime.now(),
    )

    private fun buildInternalBankAccount() = InternalBankAccount(
        accountType = AccountType.PAYMENT,
        bankNo = 4104,
        routingNo = 4714,
        accountNo = 2213,
        accountDv = "vero",
        document = "fabulas",
        bankAccountMode = BankAccountMode.PHYSICAL,
    )

    private fun buildExternalPaymentMethod() = ExternalPaymentMethod(AccountProviderName.ME_POUPE)

    private fun buildCreditCardPaymentMethod() = CreditCard(
        brand = CreditCardBrand.VISA,
        pan = "****************",
        expiryDate = "10/28",
        riskLevel = RiskLevel.LOW,
        binDetails = null,
        externalId = null,
        mainCard = true,
    )

    private fun WalletPaymentMethodsResponseTO.validatePixKey(email: String = "***********@@test.com") {
        with(pixKey!!) {
            value shouldBe email
            type shouldBe PixKeyType.EMAIL
        }
    }

    private fun WalletPaymentMethodsResponseTO.validateEvpPixKey(evp: String = evpPixKeyFixture.value) {
        with(evpPixKey!!) {
            value shouldBe evp
            type shouldBe PixKeyType.EVP
        }
    }
}