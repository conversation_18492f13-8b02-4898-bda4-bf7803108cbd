package ai.friday.billpayment.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.InviteStatus
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.generateCookie
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class ListInviteTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    private val enhancedClient = getDynamoDB()

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(WalletRepository::class)
    fun walletRepository(): WalletRepository = walletRepository

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository: WalletRepository = spyk(
        WalletDbRepository(
            walletDAO = walletDAO,
            walletMemberDAO = walletMemberDAO,
            inviteDAO = inviteDAO,
            inviteReminderDAO = inviteReminderDAO,
            walletLimitDAO = walletLimitDAO,
            accountRepository = mockk(),
        ),
    )
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val accountId = wallet.founder.accountId
    private val walletId = wallet.id
    private val founder = wallet.founder

    private fun buildListRequest(cookie: Cookie, walletId: WalletId): MutableHttpRequest<InviteRequestTO> {
        return HttpRequest.GET<InviteRequestTO>("/wallet/${walletId.value}/invites")
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private fun buildPendingInvite(): Invite {
        return Invite(
            walletId = walletId,
            memberDocument = "***********",
            memberName = "Alexandre",
            memberType = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                founderContactsEnabled = true,
                viewBalance = true,
                notification = true,
            ),
            status = InviteStatus.PENDING,
            validUntil = getLocalDate(),
            walletName = wallet.name,
            founderName = founder.name,
            founderDocument = founder.document,
            created = getZonedDateTime(),
        )
    }

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `should return not found when walletID does not exists`() {
        every {
            walletRepository.findWallet(walletId)
        } throws ItemNotFoundException("wallet não encontrada")

        val request = buildListRequest(generateCookie(accountId, Role.OWNER), walletId)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.listOf(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe InviteErrorsResponse.WalletNotFound.code
    }

    @Test
    fun `should return forbidden when accountId is not an wallet member`() {
        every {
            walletRepository.findWallet(walletId)
        } returns wallet

        every {
            walletRepository.findWalletOrNull(walletId)
        } returns wallet

        val request = buildListRequest(generateCookie(AccountId(ACCOUNT_ID_2), Role.OWNER), walletId)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.listOf(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return empty invites list when there are no invites`() {
        every {
            walletRepository.findWallet(walletId)
        } returns wallet

        every {
            walletRepository.findWalletOrNull(walletId)
        } returns wallet

        val request = buildListRequest(generateCookie(accountId, Role.OWNER), walletId)

        val response = client.toBlocking()
            .exchange(request, Argument.listOf(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        response.body.get().size shouldBe 0
    }

    @Test
    fun `should return invites list without accepted or cancelled invites`() {
        every {
            walletRepository.findWallet(walletId)
        } returns wallet

        every {
            walletRepository.findWalletOrNull(walletId)
        } returns wallet

        val pendingInvite = buildPendingInvite()

        val acceptInvite = pendingInvite.copy(
            memberDocument = "***********",
            memberName = "Eduardo",
            status = InviteStatus.ACCEPTED,
        )

        val canceledInvite = pendingInvite.copy(
            memberDocument = "***********",
            memberName = "Walter",
            status = InviteStatus.CANCELED,
        )

        walletRepository.save(pendingInvite)
        walletRepository.save(acceptInvite)
        walletRepository.save(canceledInvite)

        val request = buildListRequest(generateCookie(accountId, Role.OWNER), walletId)

        val response = client.toBlocking()
            .exchange(request, Argument.listOf(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        response.body.get().size shouldBe 1
        with(response.body.get().first()) {
            this.walletId shouldBe pendingInvite.walletId.value
            this.memberDocument shouldBe pendingInvite.memberDocument
            this.memberName shouldBe pendingInvite.memberName
            this.status shouldBe pendingInvite.status.name
            this.permissions.scheduleBills shouldBe pendingInvite.permissions.scheduleBills
            this.permissions.viewBills shouldBe pendingInvite.permissions.viewBills
            this.permissions.founderContactsEnabled shouldBe pendingInvite.permissions.founderContactsEnabled
            this.permissions.cashinEnabled shouldBe pendingInvite.permissions.cashinEnabled
            this.permissions.viewBalance shouldBe pendingInvite.permissions.viewBalance
        }
    }

    @Test
    fun `should return invites list without expired or declined invitations for more than 7 days`() {
        every {
            walletRepository.findWallet(walletId)
        } returns wallet

        every {
            walletRepository.findWalletOrNull(walletId)
        } returns wallet

        val pendingInvite = buildPendingInvite()

        val pendingOldInvite =
            pendingInvite.copy(
                validUntil = getZonedDateTime().toLocalDate().minusDays(23),
                memberDocument = "71616134003",
            )

        val pendingOldInvite2 =
            pendingInvite.copy(
                validUntil = getZonedDateTime().toLocalDate().minusDays(15),
                memberDocument = "33316134994",
            )

        val declinedInvite = pendingInvite.copy(
            memberDocument = "***********1",
            memberName = "Eduardo",
            status = InviteStatus.DECLINED,
            updatedAt = getZonedDateTime().minusDays(6),
        )

        val oldDeclinedInvite = pendingInvite.copy(
            memberDocument = "***********3",
            memberName = "Marlon",
            status = InviteStatus.DECLINED,
            updatedAt = getZonedDateTime().minusDays(8),
        )

        val olderCanceledInviteFirstIteration = pendingInvite.copy(
            memberDocument = "444444444444",
            memberName = "Felipe",
            status = InviteStatus.CANCELED,
            created = pendingInvite.created.minusMinutes(1),
            updatedAt = getZonedDateTime().minusDays(6),
        )

        val expiredInvite = pendingInvite.copy(
            memberDocument = "444444444444",
            memberName = "Felipe",
            status = InviteStatus.EXPIRED,
            updatedAt = getZonedDateTime().minusDays(6),
        )

        val oldExpiredInvite = pendingInvite.copy(
            memberDocument = "55555555555",
            memberName = "Walter",
            status = InviteStatus.EXPIRED,
            updatedAt = getZonedDateTime().minusDays(8),
        )

        walletRepository.save(pendingInvite)
        walletRepository.save(pendingOldInvite)
        walletRepository.save(pendingOldInvite2)
        walletRepository.save(declinedInvite)
        walletRepository.save(oldDeclinedInvite)
        walletRepository.save(olderCanceledInviteFirstIteration)
        walletRepository.save(expiredInvite)
        walletRepository.save(oldExpiredInvite)

        val request = buildListRequest(generateCookie(accountId, Role.OWNER), walletId)

        val response = client.toBlocking()
            .exchange(request, Argument.listOf(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK

        with(response.body()!!) {
            size shouldBe 3

            val declinedInviteResponse = find { invite -> invite.status == InviteStatus.DECLINED.name }
            with(declinedInviteResponse!!) {
                memberName shouldBe declinedInvite.memberName
                memberDocument shouldBe declinedInvite.memberDocument
            }

            val expiredInviteResponse = find { invite -> invite.status == InviteStatus.EXPIRED.name }
            with(expiredInviteResponse!!) {
                memberName shouldBe expiredInvite.memberName
                memberDocument shouldBe expiredInvite.memberDocument
            }

            val pendingInviteResponse = find { invite -> invite.status == InviteStatus.PENDING.name }
            with(pendingInviteResponse!!) {
                memberName shouldBe pendingInvite.memberName
                memberDocument shouldBe pendingInvite.memberDocument
            }
        }
    }
}