package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.register.SimpleSignUpRequest
import ai.friday.billpayment.app.register.SimpleSignUpResult
import ai.friday.billpayment.app.register.SimpleSignUpService
import ai.friday.billpayment.app.register.UserDataValidationRequest
import ai.friday.billpayment.app.register.UserDataValidationResult
import ai.friday.morning.date.dateFormat
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class B2BSimpleSignUpControllerTest(
    embeddedServer: EmbeddedServer,
) {

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @field:Property(name = "modatta-b2b.identity")
    lateinit var modattaB2BIdentity: String

    @field:Property(name = "modatta-b2b.secret")
    lateinit var modattaB2BSecret: String

    @MockBean(SimpleSignUpService::class)
    fun simpleSignUpService() = simpleSignUpService
    private val simpleSignUpService: SimpleSignUpService = mockk()

    private val userDataValidationRequestTO = UserDataValidationRequestTO(
        document = ACCOUNT.document,
        mobilePhone = ACCOUNT.mobilePhone,
    )

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    val simpleSignUpRequestTO = SimpleSignUpRequestTO(
        name = "John Doe",
        document = "***********",
        birthDate = "1970-01-01",
        email = "<EMAIL>",
        mobilePhone = "+*************",
        livenessId = "FAKE-LIVENESS-ID",
        externalId = "FAKE-EXTERNAL-ID",
        externalIdProvider = "MODATTA",
        userContractKey = "USER-CONTRACT",
        userContractSignature = "SIGNATURE-KEY",
    )

    @ParameterizedTest
    @MethodSource("userDataValidationResults")
    fun `deve repassar o resultado da validacao dos dados do usuario`(
        userDataValidationResult: UserDataValidationResult,
    ) {
        val slot = slot<UserDataValidationRequest>()
        every {
            simpleSignUpService.validate(capture(slot))
        } returns userDataValidationResult

        val request = buildPutValidate(userDataValidationRequestTO)

        val response = client.toBlocking()
            .exchange(
                request,
                Argument.of(UserDataValidationResponseTO::class.java),
                Argument.of(ResponseTO::class.java),
            )

        response.status shouldBe HttpStatus.OK

        with(response.body()) {
            valid shouldBe userDataValidationResult.valid
            accountId shouldBe userDataValidationResult.accountId?.value
        }

        with(slot.captured) {
            this.document.value shouldBe userDataValidationRequestTO.document
            this.mobilePhone.msisdn shouldBe userDataValidationRequestTO.mobilePhone
        }
    }

    @Test
    fun `deve retornar bad request ao nao receber todos os dados para o cadastro`() {
        val request = buildPostSimpleSignUp(
            SimpleSignUpRequestTO(
                name = "",
                document = "",
                birthDate = "",
                email = "",
                mobilePhone = "",
                livenessId = "",
                externalId = "",
                externalIdProvider = "",
                userContractKey = "",
                userContractSignature = "",
            ),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(SimpleSignUpResponseTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `deve retornar bad request se a resposta da validacao de telefone e cpf for diferente de valido sem accountId`() {
        val slot = slot<SimpleSignUpRequest>()
        every {
            simpleSignUpService.signUp(capture(slot))
        } returns java.lang.IllegalStateException().left()

        val request = buildPostSimpleSignUp(
            simpleSignUpRequestTO,
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(SimpleSignUpResponseTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST

        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            this.code shouldBe "4001"
            this.message shouldBe "Dados inválidos"
        }

        with(slot.captured) {
            this.document.value shouldBe simpleSignUpRequestTO.document
            this.mobilePhone.msisdn shouldBe simpleSignUpRequestTO.mobilePhone
            this.name shouldBe simpleSignUpRequestTO.name
            this.birthDate.format(dateFormat) shouldBe simpleSignUpRequestTO.birthDate
            this.email.value shouldBe simpleSignUpRequestTO.email
            this.livenessId.value shouldBe simpleSignUpRequestTO.livenessId
            this.externalId.value shouldBe simpleSignUpRequestTO.externalId
        }
    }

    @Test
    fun `deve retornar sucesso ao quando retornar com um accountId`() {
        val result = SimpleSignUpResult(
            accountId = AccountId(),
        )

        val slot = slot<SimpleSignUpRequest>()
        every {
            simpleSignUpService.signUp(capture(slot))
        } returns result.right()

        val request = buildPostSimpleSignUp(
            simpleSignUpRequestTO,
        )

        val response = client.toBlocking()
            .exchange(
                request,
                Argument.of(SimpleSignUpResponseTO::class.java),
                Argument.of(ResponseTO::class.java),
            )

        response.status shouldBe HttpStatus.OK

        with(response.body()!!) {
            this.accountId shouldNotBe null
        }

        with(slot.captured) {
            this.name shouldBe simpleSignUpRequestTO.name
            this.document.value shouldBe simpleSignUpRequestTO.document
            this.birthDate.format(dateFormat) shouldBe simpleSignUpRequestTO.birthDate
            this.email.value shouldBe simpleSignUpRequestTO.email
            this.mobilePhone.msisdn shouldBe simpleSignUpRequestTO.mobilePhone
            this.livenessId.value shouldBe simpleSignUpRequestTO.livenessId
            this.externalId.value shouldBe simpleSignUpRequestTO.externalId
            this.userContractKey shouldBe simpleSignUpRequestTO.userContractKey
            this.userContractSignature shouldBe simpleSignUpRequestTO.userContractSignature
        }
    }

    private fun buildPutValidate(
        userDataTO: UserDataValidationRequestTO,
    ): MutableHttpRequest<UserDataValidationRequestTO> {
        return HttpRequest.PUT("/b2b/simpleSignUp/validate", userDataTO)
            .basicAuth(modattaB2BIdentity, modattaB2BSecret)
    }

    private fun buildPostSimpleSignUp(
        simpleSignUpRequestTO: SimpleSignUpRequestTO,
    ): MutableHttpRequest<SimpleSignUpRequestTO> {
        return HttpRequest.POST("/b2b/simpleSignUp", simpleSignUpRequestTO)
            .basicAuth(modattaB2BIdentity, modattaB2BSecret)
    }

    companion object {
        @JvmStatic
        fun userDataValidationResults(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(UserDataValidationResult(valid = false, accountId = null)),
                Arguments.of(UserDataValidationResult(valid = true, accountId = null)),
                Arguments.of(UserDataValidationResult(valid = true, accountId = AccountId("FAKE"))),
            )
        }
    }
}