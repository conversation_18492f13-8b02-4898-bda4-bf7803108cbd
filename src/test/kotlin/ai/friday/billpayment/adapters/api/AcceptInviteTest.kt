package ai.friday.billpayment.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.ActionSourceCriteria
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.CriteriaForViewingBills
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.InviteAnswer
import ai.friday.billpayment.app.wallet.InviteStatus
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberAdded
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.EMAIL
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.NAME
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.generateCookie
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.paymentMethodId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import io.kotest.inspectors.forAll
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

@MicronautTest(environments = [FRIDAY_ENV])
class AcceptInviteTest(
    embeddedServer: EmbeddedServer,
) {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val enhancedClient = getDynamoDB()

    @MockBean(CrmService::class)
    fun crmService() = crmService

    private val crmService = mockk<CrmService>(relaxed = true)

    @MockBean(WalletRepository::class)
    fun walletRepository(): WalletRepository = walletRepository

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository: WalletRepository = spyk(
        WalletDbRepository(
            walletDAO = walletDAO,
            walletMemberDAO = walletMemberDAO,
            inviteDAO = inviteDAO,
            inviteReminderDAO = inviteReminderDAO,
            walletLimitDAO = walletLimitDAO,
            accountRepository = mockk(),
        ),
    )

    @MockBean(NotificationAdapter::class)
    fun notificationAdapter(): NotificationAdapter = notificationAdapter

    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService

    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(EventPublisher::class)
    fun eventPublisher() = eventPublisher
    private val eventPublisher: EventPublisher = mockk(relaxUnitFun = true)

    @MockBean(DeviceFingerprintService::class)
    fun deviceFingerprintService() = deviceFingerprintService
    private val deviceFingerprintService: DeviceFingerprintService = mockk(relaxed = true)

    private val client = RxHttpClient.create(embeddedServer.url)

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val inviteeAccountId = AccountId(ACCOUNT_ID)
    private val founderAccountId = wallet.founder.accountId
    private val walletId = wallet.id
    private val founder = wallet.founder

    private val internalBankAccount = walletFixture.buildBankAccount(wallet)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        loadAccountIntoDb(dynamoDB, accountId = inviteeAccountId)
    }

    @ParameterizedTest
    @EnumSource(InviteAnswer::class)
    fun `should return not found when invite does not exist`(inviteAnswer: InviteAnswer) {
        val request =
            buildRequest(generateCookie(inviteeAccountId, Role.OWNER), walletId, InviteAnswerTO(action = inviteAnswer))

        val thrown =
            assertThrows<HttpClientResponseException> {
                client
                    .toBlocking()
                    .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
            }

        thrown.status shouldBe HttpStatus.NOT_FOUND

        verify(exactly = 0) {
            eventPublisher.publish(any(MemberAdded::class))
        }
    }

    @ParameterizedTest
    @EnumSource(InviteStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PENDING"])
    fun `should return gone when accepting an invite that is not pending`(inviteStatus: InviteStatus) {
        shouldReturnGoneWhenRespondingANotPendingInvite(inviteStatus, InviteAnswer.ACCEPT)
    }

    @ParameterizedTest
    @EnumSource(InviteStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PENDING"])
    fun `should return gone when declining an invite that is not pending`(inviteStatus: InviteStatus) {
        shouldReturnGoneWhenRespondingANotPendingInvite(inviteStatus, InviteAnswer.DECLINE)
    }

    @Test
    fun `should accept invite`() {
        val founderMobilePhone = "*************"
        val founderName = "Fulano de Tal"
        loadAccountIntoDb(dynamoDB, accountId = founderAccountId, mobilePhone = founderMobilePhone, name = founderName)
        walletRepository.save(wallet)
        val pendingInvite =
            Invite(
                walletId = walletId,
                memberDocument = DOCUMENT,
                memberName = NAME,
                memberType = MemberType.ASSISTANT,
                status = InviteStatus.PENDING,
                validUntil = getZonedDateTime().plusDays(1).toLocalDate(),
                walletName = wallet.name,
                founderName = founder.name,
                founderDocument = founder.document,
                created = getZonedDateTime(),
            )
        walletRepository.save(pendingInvite)
        val request =
            buildRequest(
                generateCookie(inviteeAccountId, Role.OWNER),
                walletId,
                InviteAnswerTO(action = InviteAnswer.ACCEPT),
            )

        val startZonedDate = ZonedDateTime.of(2021, 1, 30, 0, 0, 0, 0, brazilTimeZone)
        withGivenDateTime(startZonedDate) {
            val response =
                client
                    .toBlocking()
                    .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
            response.status shouldBe HttpStatus.NO_CONTENT
            val invite = walletRepository.findInvite(walletId = walletId, memberDocument = DOCUMENT)
            invite.status shouldBe InviteStatus.ACCEPTED
            invite.created shouldBe pendingInvite.created
            invite.updatedAt shouldBe startZonedDate
            invite.validUntil shouldBe pendingInvite.validUntil
            val wallet = walletRepository.findWallet(walletId)
            wallet.activeMembers shouldContain
                Member(
                    accountId = inviteeAccountId,
                    document = DOCUMENT,
                    name = NAME,
                    emailAddress = EmailAddress(EMAIL),
                    type = MemberType.ASSISTANT,
                    status = MemberStatus.ACTIVE,
                    permissions = MemberPermissions.of(pendingInvite.memberType),
                    created = startZonedDate,
                    updated = startZonedDate,
                )
        }

        verify {
            eventPublisher.publish(any(MemberAdded::class))
        }

        val founderSlot = slot<Member>()
        verify {
            notificationAdapter.notifyWalletMemberJoined(
                founder = capture(founderSlot),
                inviteeFullName = NAME,
                walletName = wallet.name,
            )
        }
        with(founderSlot.captured) {
            this.accountId shouldBe founder.accountId
        }
    }

    @Test
    fun `should decline invite`() {
        walletRepository.save(wallet)
        val pendingInvite =
            Invite(
                walletId = walletId,
                memberDocument = DOCUMENT,
                memberName = NAME,
                memberType = MemberType.COLLABORATOR,
                status = InviteStatus.PENDING,
                validUntil = getLocalDate().plusDays(1),
                walletName = wallet.name,
                founderName = founder.name,
                founderDocument = founder.document,
                created = getZonedDateTime(),
            )

        every {
            walletRepository.findAccountPaymentMethod(any())
        } returns internalBankAccount

        walletRepository.save(pendingInvite)
        val request =
            buildRequest(
                generateCookie(inviteeAccountId, Role.OWNER),
                walletId,
                InviteAnswerTO(action = InviteAnswer.DECLINE),
            )

        val startZonedDate = ZonedDateTime.of(2021, 1, 30, 0, 0, 0, 0, brazilTimeZone)
        withGivenDateTime(startZonedDate) {
            val response =
                client
                    .toBlocking()
                    .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
            response.status shouldBe HttpStatus.NO_CONTENT
            val invite =
                walletRepository.findInvite(
                    walletId = walletId,
                    memberDocument = DOCUMENT,
                )
            invite.status shouldBe InviteStatus.DECLINED
            invite.created shouldBe pendingInvite.created
            invite.updatedAt shouldBe startZonedDate
            invite.validUntil shouldBe pendingInvite.validUntil
            val wallet = walletRepository.findWallet(walletId)
            wallet.allMembers.forAll {
                it.document shouldNotBe pendingInvite.memberDocument
            }
        }

        verify(exactly = 0) {
            eventPublisher.publish(any(MemberAdded::class))
        }
    }

    @Test
    fun `should do nothing when member already exists`() {
        val founderMobilePhone = "*************"
        val founderName = "Fulano de Tal"
        val walletFixture = WalletFixture()

        val wallet =
            Wallet(
                walletId,
                "carteira",
                listOf(founder, walletFixture.participant),
                10,
                WalletStatus.ACTIVE,
                WalletType.PRIMARY,
                paymentMethodId,
            )

        loadAccountIntoDb(
            dynamoDB,
            accountId = founderAccountId,
            mobilePhone = founderMobilePhone,
            name = founderName,
        )
        loadAccountIntoDb(
            dynamoDB,
            accountId = walletFixture.participantAccount.accountId,
            document = walletFixture.participantAccount.document,
        )
        walletRepository.save(wallet)

        val pendingInvite =
            Invite(
                walletId = wallet.id,
                memberDocument = walletFixture.participant.document,
                memberName = walletFixture.participant.name,
                memberType = MemberType.COLLABORATOR,
                status = InviteStatus.PENDING,
                validUntil = getLocalDate().plusDays(1),
                walletName = wallet.name,
                founderName = founder.name,
                founderDocument = founder.document,
                created = getZonedDateTime(),
            )
        walletRepository.save(pendingInvite)

        val request =
            buildRequest(
                generateCookie(walletFixture.participant.accountId, Role.OWNER),
                walletId,
                InviteAnswerTO(action = InviteAnswer.ACCEPT),
            )

        val startZonedDate = ZonedDateTime.of(2021, 1, 30, 0, 0, 0, 0, brazilTimeZone)
        withGivenDateTime(startZonedDate) {
            val response =
                client
                    .toBlocking()
                    .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
            response.status shouldBe HttpStatus.NO_CONTENT
            val invite =
                walletRepository.findInvite(walletId = walletId, memberDocument = walletFixture.participant.document)
            invite.status shouldBe InviteStatus.ACCEPTED
            invite.created shouldBe pendingInvite.created
            invite.updatedAt shouldBe startZonedDate
            invite.validUntil shouldBe pendingInvite.validUntil
        }

        verify(exactly = 0) {
            eventPublisher.publish(any(MemberAdded::class))
        }
    }

    @Test
    fun `should add when member with REMOVED status already exists`() {
        val founderMobilePhone = "*************"
        val founderName = "Fulano de Tal"
        val walletFixture = WalletFixture()

        val wallet =
            Wallet(
                walletId,
                "carteira",
                listOf(founder, walletFixture.participant.copy(status = MemberStatus.REMOVED)),
                10,
                WalletStatus.ACTIVE,
                WalletType.PRIMARY,
                paymentMethodId,
            )

        loadAccountIntoDb(
            dynamoDB,
            accountId = founderAccountId,
            mobilePhone = founderMobilePhone,
            name = founderName,
        )
        loadAccountIntoDb(
            dynamoDB,
            accountId = walletFixture.participantAccount.accountId,
            document = walletFixture.participantAccount.document,
        )
        walletRepository.save(wallet)

        val pendingInvite =
            Invite(
                walletId = wallet.id,
                memberDocument = walletFixture.participant.document,
                memberName = walletFixture.participant.name,
                memberType = MemberType.COLLABORATOR,
                status = InviteStatus.PENDING,
                validUntil = getLocalDate().plusDays(1),
                walletName = wallet.name,
                founderName = founder.name,
                founderDocument = founder.document,
                created = getZonedDateTime(),
            )
        walletRepository.save(pendingInvite)

        val request =
            buildRequest(
                generateCookie(walletFixture.participant.accountId, Role.OWNER),
                walletId,
                InviteAnswerTO(action = InviteAnswer.ACCEPT),
            )

        val startZonedDate = ZonedDateTime.of(2021, 1, 30, 0, 0, 0, 0, brazilTimeZone)
        withGivenDateTime(startZonedDate) {
            val response =
                client
                    .toBlocking()
                    .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
            response.status shouldBe HttpStatus.NO_CONTENT
            val invite =
                walletRepository.findInvite(walletId = walletId, memberDocument = walletFixture.participant.document)
            invite.status shouldBe InviteStatus.ACCEPTED
            invite.created shouldBe pendingInvite.created
            invite.updatedAt shouldBe startZonedDate
            invite.validUntil shouldBe pendingInvite.validUntil

            val walletResult = walletRepository.findWallet(walletId)
            walletResult.getActiveMemberOrNull(walletFixture.participant.accountId) shouldNotBe null

            verify {
                eventPublisher.publish(any(MemberAdded::class))
            }
        }
    }

    private fun shouldReturnGoneWhenRespondingANotPendingInvite(
        inviteStatus: InviteStatus,
        inviteAnswer: InviteAnswer,
    ) {
        val invite =
            Invite(
                walletId = walletId,
                memberDocument = DOCUMENT,
                memberName = "Alexandre",
                memberType = MemberType.COLLABORATOR,
                status = inviteStatus,
                validUntil = getLocalDate(),
                walletName = wallet.name,
                founderName = founder.name,
                founderDocument = founder.document,
                created = getZonedDateTime(),
            )
        walletRepository.save(invite)

        val request =
            buildRequest(generateCookie(inviteeAccountId, Role.OWNER), walletId, InviteAnswerTO(action = inviteAnswer))

        val thrown =
            assertThrows<HttpClientResponseException> {
                client
                    .toBlocking()
                    .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
            }

        verify(exactly = 0) {
            eventPublisher.publish(any(MemberAdded::class))
        }

        thrown.status shouldBe HttpStatus.GONE
    }

    @Test
    fun `should accept invite with custom permissions`() {
        val founderMobilePhone = "*************"
        val founderName = "Fulano de Tal"
        loadAccountIntoDb(dynamoDB, accountId = founderAccountId, mobilePhone = founderMobilePhone, name = founderName)
        walletRepository.save(wallet)
        val customPermissions =
            MemberPermissions(
                viewBills = BillPermission.ALL_BILLS,
                scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                founderContactsEnabled = false,
                viewBalance = true,
                notification = true,
            )
        val pendingInvite =
            Invite(
                walletId = walletId,
                memberDocument = DOCUMENT,
                memberName = NAME,
                memberType = MemberType.ASSISTANT,
                status = InviteStatus.PENDING,
                validUntil = getZonedDateTime().plusDays(1).toLocalDate(),
                walletName = wallet.name,
                founderName = founder.name,
                founderDocument = founder.document,
                created = getZonedDateTime(),
                permissions = customPermissions,
            )
        walletRepository.save(pendingInvite)
        val request =
            buildRequest(
                generateCookie(inviteeAccountId, Role.OWNER),
                walletId,
                InviteAnswerTO(action = InviteAnswer.ACCEPT),
            )

        val startZonedDate = ZonedDateTime.of(2021, 1, 30, 0, 0, 0, 0, brazilTimeZone)
        withGivenDateTime(startZonedDate) {
            val response =
                client
                    .toBlocking()
                    .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
            response.status shouldBe HttpStatus.NO_CONTENT

            val wallet = walletRepository.findWallet(walletId)

            wallet.getActiveMember(inviteeAccountId).permissions shouldBe customPermissions
        }

        verify {
            eventPublisher.publish(any(MemberAdded::class))
        }
    }

    @Test
    fun `should accept invite with custom permissions by criteria`() {
        val founderMobilePhone = "*************"
        val founderName = "Fulano de Tal"
        loadAccountIntoDb(dynamoDB, accountId = founderAccountId, mobilePhone = founderMobilePhone, name = founderName)
        walletRepository.save(wallet)
        val customPermissions =
            MemberPermissions(
                viewBills = BillPermission.BY_CRITERIA,
                scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                criteriaForViewingBills =
                CriteriaForViewingBills(
                    criterias = listOf(ActionSourceCriteria("DDA")),
                ),
                founderContactsEnabled = false,
                viewBalance = true,
                notification = true,
            )
        val pendingInvite =
            Invite(
                walletId = walletId,
                memberDocument = DOCUMENT,
                memberName = NAME,
                memberType = MemberType.ASSISTANT,
                status = InviteStatus.PENDING,
                validUntil = getZonedDateTime().plusDays(1).toLocalDate(),
                walletName = wallet.name,
                founderName = founder.name,
                founderDocument = founder.document,
                created = getZonedDateTime(),
                permissions = customPermissions,
            )
        walletRepository.save(pendingInvite)
        val request =
            buildRequest(
                generateCookie(inviteeAccountId, Role.OWNER),
                walletId,
                InviteAnswerTO(action = InviteAnswer.ACCEPT),
            )

        val startZonedDate = ZonedDateTime.of(2021, 1, 30, 0, 0, 0, 0, brazilTimeZone)
        withGivenDateTime(startZonedDate) {
            val response =
                client
                    .toBlocking()
                    .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
            response.status shouldBe HttpStatus.NO_CONTENT

            val wallet = walletRepository.findWallet(walletId)

            with(wallet.getActiveMember(inviteeAccountId)) {
                permissions shouldBe customPermissions
                with(permissions.criteriaForViewingBills.shouldNotBeNull()) {
                    criterias.first() shouldBe ActionSourceCriteria("DDA")
                }
            }
        }

        verify {
            eventPublisher.publish(any(MemberAdded::class))
        }
    }

    private fun buildRequest(
        cookie: Cookie,
        walletId: WalletId,
        inviteAnswerTO: InviteAnswerTO,
    ): MutableHttpRequest<*> =
        HttpRequest
            .PUT("/wallet/${walletId.value}/invite", inviteAnswerTO)
            .cookie(cookie)
            .header("X-API-VERSION", "2")
}