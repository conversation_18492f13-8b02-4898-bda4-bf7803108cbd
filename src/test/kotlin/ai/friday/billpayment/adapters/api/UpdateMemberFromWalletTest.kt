package ai.friday.billpayment.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.CustomPermissionsMemberType
import ai.friday.billpayment.app.wallet.CustomPermissionsOptions
import ai.friday.billpayment.app.wallet.CustomViewBillPermission
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.buildCookie
import ai.friday.billpayment.integration.createBillPaymentTable
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.clearMocks
import io.mockk.mockk
import io.mockk.spyk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class UpdateMemberFromWalletTest(embeddedServer: EmbeddedServer) {

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val enhancedClient = getDynamoDB()

    @MockBean(WalletRepository::class)
    fun walletRepository(): WalletRepository = walletRepository

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository: WalletRepository = spyk(
        WalletDbRepository(
            walletDAO = walletDAO,
            walletMemberDAO = walletMemberDAO,
            inviteDAO = inviteDAO,
            inviteReminderDAO = inviteReminderDAO,
            walletLimitDAO = walletLimitDAO,
            accountRepository = mockk(),
        ),
    )

    private val walletFixture = WalletFixture()

    private val wallet = walletFixture.buildWallet(
        otherMembers = listOf(
            walletFixture.participant,
            walletFixture.cofounder,
            walletFixture.limitedParticipant,
            walletFixture.removedParticipant,
            walletFixture.ultraLimitedParticipant,
            walletFixture.cantPayParticipant,
            walletFixture.assistant,
        ),
    )

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        walletRepository.save(wallet)
        clearMocks(walletRepository)
    }

    @Test
    fun `should return NOT FOUND when wallet does not exist`() {
        val request = buildRequest(
            walletId = WalletId("NAO_EXISTE"),
            currentAccount = walletFixture.founderAccount,
            targetAccountId = walletFixture.participant.accountId,
            memberType = CustomPermissionsMemberType.COLLABORATOR,
            viewBills = CustomViewBillPermission.ONLY_BILLS_ADDED_BY_USER,
        )

        val response =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, Argument.VOID) }

        response.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `should return UNPROCESSABLE ENTITY when user is not a wallet member`() {
        val request = buildRequest(
            walletId = wallet.id,
            currentAccount = walletFixture.participantAccount,
            targetAccountId = walletFixture.participant.accountId,
            memberType = CustomPermissionsMemberType.ASSISTANT,
            viewBills = CustomViewBillPermission.ONLY_BILLS_ADDED_BY_USER,
        )

        val response =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, Argument.VOID) }

        response.status shouldBe HttpStatus.UNPROCESSABLE_ENTITY
    }

    @Test
    fun `should return UNPROCESSABLE ENTITY when user cannot manage members`() {
        val request = buildRequest(
            walletId = wallet.id,
            currentAccount = walletFixture.limitedParticipantAccount,
            targetAccountId = walletFixture.participant.accountId,
            memberType = CustomPermissionsMemberType.ASSISTANT,
            viewBills = CustomViewBillPermission.ONLY_BILLS_ADDED_BY_USER,
        )

        val response =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, Argument.VOID) }

        response.status shouldBe HttpStatus.UNPROCESSABLE_ENTITY
    }

    @Test
    fun `should return NOT FOUND when updated user is not a wallet member`() {
        val request = buildRequest(
            walletId = wallet.id,
            currentAccount = walletFixture.founderAccount,
            targetAccountId = walletFixture.removedParticipant.accountId,
            memberType = CustomPermissionsMemberType.ASSISTANT,
            viewBills = CustomViewBillPermission.ONLY_BILLS_ADDED_BY_USER,
        )

        val response =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, Argument.VOID) }

        response.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `should return UNPROCESSABLE ENTITY when member tries to update itself`() {
        val request = buildRequest(
            walletId = wallet.id,
            currentAccount = walletFixture.cofounderAccount,
            targetAccountId = walletFixture.cofounderAccount.accountId,
            memberType = CustomPermissionsMemberType.ASSISTANT,
            viewBills = CustomViewBillPermission.ONLY_BILLS_ADDED_BY_USER,
        )

        val response =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, Argument.VOID) }

        response.status shouldBe HttpStatus.UNPROCESSABLE_ENTITY
    }

    @Test
    fun `should return NO CONTENT when founder tries to updated a member to COFOUNDER and change its permissions`() {
        val request = buildRequest(
            walletId = wallet.id,
            currentAccount = walletFixture.founderAccount,
            targetAccountId = walletFixture.cantPayParticipant.accountId,
            memberType = CustomPermissionsMemberType.COFOUNDER,
            viewBills = CustomViewBillPermission.ONLY_BILLS_ADDED_BY_USER,
        )

        val response = client.toBlocking().exchange(request, Argument.of(Unit::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        with(
            walletRepository.findWallet(walletId = wallet.id)
                .getActiveMember(walletFixture.cantPayParticipant.accountId),
        ) {
            this.type shouldBe MemberType.COFOUNDER
            this.permissions shouldBe MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.ALL_BILLS,
                founderContactsEnabled = false,
                viewBalance = true,
                manageMembers = true,
                notification = true,
            )
        }
    }

    @Test
    fun `should return NO CONTENT when a member who can manage members updates a member to COLLABORATOR and change its permissions`() {
        val request = buildRequest(
            walletId = wallet.id,
            currentAccount = walletFixture.cofounderAccount,
            targetAccountId = walletFixture.assistantAccount.accountId,
            memberType = CustomPermissionsMemberType.COLLABORATOR,
            viewBills = CustomViewBillPermission.ALL_BILLS,
        )

        val response = client.toBlocking().exchange(request, Argument.of(Unit::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        with(
            walletRepository.findWallet(walletId = wallet.id)
                .getActiveMember(walletFixture.assistantAccount.accountId),
        ) {
            this.type shouldBe MemberType.COLLABORATOR
            this.permissions shouldBe MemberPermissions(
                manageMembers = false,
                viewBills = BillPermission.ALL_BILLS,
                scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                founderContactsEnabled = false,
                viewBalance = true,
                notification = true,
            )
        }
    }

    @Test
    fun `should return NO CONTENT when a member who can manage members updates a member to ASSISTANT and change its permissions`() {
        val request = buildRequest(
            walletId = wallet.id,
            currentAccount = walletFixture.founderAccount,
            targetAccountId = walletFixture.cantPayParticipant.accountId,
            memberType = CustomPermissionsMemberType.ASSISTANT,
            viewBills = CustomViewBillPermission.ALL_BILLS,
        )

        val response = client.toBlocking().exchange(request, Argument.of(Unit::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        with(
            walletRepository.findWallet(walletId = wallet.id)
                .getActiveMember(walletFixture.cantPayParticipant.accountId),
        ) {
            this.type shouldBe MemberType.ASSISTANT
            this.permissions shouldBe MemberPermissions(
                viewBills = BillPermission.ALL_BILLS,
                scheduleBills = BillPermission.NO_BILLS,
                founderContactsEnabled = true,
                viewBalance = false,
                notification = true,
            )
        }
    }

    private fun buildRequest(
        walletId: WalletId,
        currentAccount: Account,
        targetAccountId: AccountId,
        memberType: CustomPermissionsMemberType,
        viewBills: CustomViewBillPermission,
    ) = HttpRequest.PUT(
        "/wallet/${walletId.value}/member/${targetAccountId.value}",
        CustomPermissionsOptions(memberType = memberType, viewBills = viewBills),
    ).cookie(buildCookie(account = currentAccount)).header("X-API-VERSION", "2")
}