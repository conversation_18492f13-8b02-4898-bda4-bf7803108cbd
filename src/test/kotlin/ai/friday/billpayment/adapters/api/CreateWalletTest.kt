package ai.friday.billpayment.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.accountRegisterDataWithAddress
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.Gender
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.PoliticallyExposed
import ai.friday.billpayment.app.account.RegisterNaturalPersonResponse
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.PixKeyServiceInterface
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.cnhDocumentInfo
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.generateCookie
import ai.friday.billpayment.mobilePhone
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.string.shouldContain
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

@MicronautTest(environments = [FRIDAY_ENV])
class CreateWalletTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {
    private val enhancedClient = getDynamoDB()

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountDbRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val accountRegisterDAO = AccountRegisterDynamoDAO(enhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(enhancedClient)
    private val accountRegisterRepository = AccountRegisterDbRepository(
        accountRegisterDAO = accountRegisterDAO,
        originalOcrAndPersonDataDAO = originalOcrAndPersonDataDAO,
        objectRepository = mockk(),
    )
    private val pixKeyManagement: PixKeyManagement = mockk()
    private val pixKeyService: PixKeyServiceInterface = mockk()
    private val walletFixture = WalletFixture()
    private val externalAccountRegister: ExternalAccountRegister = mockk()
    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private fun buildRequest(cookie: Cookie, createWalletTO: CreateWalletTO): MutableHttpRequest<*> {
        return HttpRequest.POST("/wallet", createWalletTO)
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private val accountRegisterData = AccountRegisterData(
        accountId = walletFixture.assistantAccount.accountId,
        emailAddress = walletFixture.assistantAccount.emailAddress,
        mobilePhone = mobilePhone,
        mobilePhoneVerified = false,
        livenessId = null,
        externalId = null,
        documentInfo = cnhDocumentInfo,
        monthlyIncome = MonthlyIncome(10000, 100000),
        nickname = "John Doe",
        mobilePhoneTokenExpiration = null,
        address = accountRegisterDataWithAddress.address,
        politicallyExposed = PoliticallyExposed(false, null),
        calculatedGender = Gender.F,
        isDocumentEdited = false,
        uploadedCNH = null,
        uploadedDocument = null,
        uploadedSelfie = null,
        agreementData = null,
        kycFile = null,
        openForUserReview = false,
        openedForUserReviewAt = null,
        lastUpdated = ZonedDateTime.now(),
        created = ZonedDateTime.now(),
        riskAnalysisFailedReasons = null,
        riskAnalysisFailedReasonsHistory = null,
        registrationType = RegistrationType.FULL,
        fraudListMatch = false,
    )

    @MockBean(ExternalAccountRegister::class)
    fun externalAccountRegister(): ExternalAccountRegister = externalAccountRegister

    @MockBean(PixKeyManagement::class)
    fun pixKeyManagement(): PixKeyManagement = pixKeyManagement

    @MockBean(PixKeyServiceInterface::class)
    fun pixKeyService(): PixKeyServiceInterface = pixKeyService

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk {
        every {
            createDefaultWalletCategories(any())
        } returns emptyList<WalletBillCategory>().right()

        every {
            findWalletCategories(any())
        } returns emptyList()
    }

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        every {
            pixKeyManagement.registerKey(any(), any(), any(), any(), any())
        } returns PixKey(value = "", type = PixKeyType.EVP)

        every {
            pixKeyService.createPixKey(any(), any())
        } returns Unit.right()

        every { externalAccountRegister.registerNaturalPerson(any()) } returns RegisterNaturalPersonResponse(
            success = true,
            accountNumber = 12345,
            accountDv = "1",
        )
    }

    @ParameterizedTest
    @ValueSource(strings = ["", "ABC DEFGH IJ **********"])
    fun `should return bad request when wallet name is empty or greater than 20 chars`(name: String) {
        val createWalletTO = CreateWalletTO(name = name)

        val request = buildRequest(generateCookie(AccountId(ACCOUNT_ID), Role.OWNER), createWalletTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(WalletTO::class.java), Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.message shouldContain "Invalid wallet name size"
    }

    @ParameterizedTest
    @ValueSource(strings = ["Carteira do $$$", "chope sim | nao", "eu, você e ele", "Carteira!", "CARTEIRA@VIA1"])
    fun `should return bad request when wallet name has invalid chars`(name: String) {
        val createWalletTO = CreateWalletTO(name = name)

        val request = buildRequest(generateCookie(AccountId(ACCOUNT_ID), Role.OWNER), createWalletTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(WalletTO::class.java), Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.message shouldContain "Wallet name with invalid characters"
    }

    @Test
    fun `should return bad request when user already reached max number of wallets`() {
        accountRegisterRepository.save(accountRegisterData.copy(accountId = walletFixture.founderAccount.accountId))
        accountDbRepository.create(walletFixture.founderAccount)
        repeat(5) {
            val wallet = walletFixture.buildWallet(name = "Carteira $it", id = WalletId("WALLET-$it"))
            walletRepository.save(wallet)
        }
        val createWalletTO = CreateWalletTO(name = "Carteira de teste")
        val request = buildRequest(generateCookie(walletFixture.founderAccount.accountId, Role.OWNER), createWalletTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(WalletTO::class.java), Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4001"
    }

    @Test
    fun `should return bad request when wallet name already exists`() {
        accountRegisterRepository.save(accountRegisterData.copy(accountId = walletFixture.founderAccount.accountId))
        accountDbRepository.create(walletFixture.founderAccount)
        val wallet = walletFixture.buildWallet(name = "Carteira de TESTE")
        walletRepository.save(wallet)

        val createWalletTO = CreateWalletTO(name = "Carteira de teste")
        val request = buildRequest(generateCookie(walletFixture.founderAccount.accountId, Role.OWNER), createWalletTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(WalletTO::class.java), Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4002"
    }

    @Test
    fun `should return created when wallet is valid`() {
        accountRegisterRepository.save(accountRegisterData.copy(accountId = walletFixture.founderAccount.accountId))
        accountDbRepository.create(walletFixture.founderAccount)
        val createWalletTO = CreateWalletTO(name = "Carteira de teste")
        val request = buildRequest(generateCookie(walletFixture.founderAccount.accountId, Role.OWNER), createWalletTO)

        val response =
            client.toBlocking().exchange(request, Argument.of(WalletTO::class.java), Argument.of(String::class.java))

        response.status shouldBe HttpStatus.CREATED
        with(response.body.get()) {
            name shouldBe createWalletTO.name
            theme shouldNotBe null
            members.size shouldBe 1
            canBeSetAsDefault shouldBe true
            isDefault shouldBe false
            with(members.single()) {
                type shouldBe MemberType.FOUNDER
                accountId shouldBe walletFixture.founderAccount.accountId.value
                alias shouldBe walletFixture.founder.name
                document shouldBe walletFixture.founder.document
                name shouldBe walletFixture.founder.name
                permissions.cashinEnabled shouldBe true
                permissions.viewBalance shouldBe true
                permissions.founderContactsEnabled shouldBe false
                permissions.scheduleBills shouldBe BillPermission.ALL_BILLS
                permissions.viewBills shouldBe BillPermission.ALL_BILLS
            }
        }
    }

    @Test
    fun `should return created when user does not have more than 5 wallets as founder`() {
        repeat(5) {
            val wallet = walletFixture.buildWallet(
                name = "Carteira $it",
                id = WalletId("WALLET-$it"),
                otherMembers = listOf(walletFixture.assistant),
            )
            walletRepository.save(wallet)
        }

        accountDbRepository.create(walletFixture.assistantAccount)
        accountRegisterRepository.save(accountRegisterData)
        val createWalletTO = CreateWalletTO(name = "Carteira de teste")
        val request = buildRequest(generateCookie(walletFixture.assistantAccount.accountId, Role.OWNER), createWalletTO)

        val response =
            client.toBlocking().exchange(request, Argument.of(WalletTO::class.java), Argument.of(String::class.java))

        response.status shouldBe HttpStatus.CREATED
        with(response.body.get()) {
            name shouldBe createWalletTO.name
            theme shouldNotBe null
            members.size shouldBe 1
            canBeSetAsDefault shouldBe true
            isDefault shouldBe false
            with(members.single()) {
                type shouldBe MemberType.FOUNDER
                accountId shouldBe walletFixture.assistantAccount.accountId.value
                alias shouldBe walletFixture.assistant.name
                document shouldBe walletFixture.assistant.document
                name shouldBe walletFixture.assistant.name
                permissions.cashinEnabled shouldBe true
                permissions.viewBalance shouldBe true
                permissions.founderContactsEnabled shouldBe false
                permissions.scheduleBills shouldBe BillPermission.ALL_BILLS
                permissions.viewBills shouldBe BillPermission.ALL_BILLS
            }
        }
    }

    /*
        [X] carteira com nome em branco -> bad request
        [X] carteira com nome maior que 20 caracteres -> bad request
        [X] carteira com nome com caracteres invalidos
        [X] 6a carteira para o mesmo usuario -> bad request
        [X] considerar somente carteiras criadas pelo proprio usuario
        [X] carteira com mesmo nome de uma existente para o usuário -> bad request
        [ ] arbi recusa criacao da conta -> bad request
        [ ] erro criando conta no arbi -> server error
        [.] carteira com emoji no nome -> created, ACTIVE

        como tratar o caso do usuário não ter todos os dados necessários para criação de conta no arbi? ex: renda mensal ainda não foi informada
     */
}