package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.messaging.settlement.PaidSettlementClientResponseTO
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.integrations.MessagePublisher
import io.micronaut.http.HttpResponse
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

@MicronautTest
class SettlementResponseControllerTest {

    private val messagePublisher = mockk<MessagePublisher>(relaxed = true)
    private val configuration = mockk<SQSMessageHandlerConfiguration>()

    @Test
    fun `should send settlement response to queue`() {
        // Given
        val queueName = "test-settlement-response-queue"
        every { configuration.settlementResponseQueueName } returns queueName

        val controller = SettlementResponseController(messagePublisher, configuration)

        val settlementResponse = PaidSettlementClientResponseTO(
            transactionId = "123456",
            barcode = "12345678901234567890123456789012345678901234",
            digitableLine = "12345678901234567890123456789012345678901234",
            authorization = "AUTH123",
            timestamp = "2023-01-01T12:00:00Z",
            financialServiceGateway = FinancialServiceGateway.FRIDAY,
            settlementFinancialInstitution = "Test Bank",
        )

        // When
        val response = controller.handleSettlementResponse(settlementResponse)

        // Then
        assertEquals(HttpResponse.ok<Any>().status, response.status)
        verify(exactly = 1) {
            messagePublisher.sendMessage(
                queueName = queueName,
                body = settlementResponse,
            )
        }
    }
}