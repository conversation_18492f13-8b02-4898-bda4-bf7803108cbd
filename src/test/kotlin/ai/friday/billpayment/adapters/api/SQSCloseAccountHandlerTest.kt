package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.CloseAccountMessage
import ai.friday.billpayment.app.account.CloseFounderWalletCloseAccountStep
import io.kotest.matchers.collections.shouldContainExactly
import org.junit.jupiter.api.Test

class SQSCloseAccountHandlerTest {

    @Test
    fun `deve fazer o parse de mensagens antigas`() {
        val message =
            """{"accountId":{"value":"ACCOUNT-4828265b-6fee-4101-8b3a-3aa039891499"},"steps":[{"@c":".SimpleCloseAccountStep","type":"Unsubscribe","status":"Pending","errorMessage":null},{"@c":".SimpleCloseAccountStep","type":"RemoveFromDDA","status":"Pending","errorMessage":null},{"@c":".SimpleCloseAccountStep","type":"SignOutUser","status":"Pending","errorMessage":null},{"@c":".CloseFounderWalletCloseAccountStep","walletId":{"value":""},"steps":[{"type":"IgnoreRecurrences","status":"Pending","errorMessage":null},{"type":"DeleteWalletPixKey","status":"Pending","errorMessage":null}],"status":"Pending","errorMessage":null,"type":"CloseFounderWallet"}],"tryCount":0}"""
        val data = parseObjectFromCloseAccount<CloseAccountMessage>(message)
        data.steps.map {
            it.type.value
        }.shouldContainExactly("Unsubscribe", "RemoveFromDDA", "SignOutUser", "CloseFounderWallet")

        (data.steps[3] as CloseFounderWalletCloseAccountStep).steps.map { it.type.value }.shouldContainExactly("IgnoreRecurrences", "DeleteWalletPixKey")
    }

    @Test
    fun `deve fazer o parse de mensagens novas`() {
        val message =
            """{"accountId":{"value":"ACCOUNT-4828265b-6fee-4101-8b3a-3aa039891499"},"steps":[{"@c":".SimpleCloseAccountStep","type":{"value":"Unsubscribe"},"status":"Pending","errorMessage":null},{"@c":".SimpleCloseAccountStep","type":{"value":"RemoveFromDDA"},"status":"Pending","errorMessage":null},{"@c":".SimpleCloseAccountStep","type":{"value":"SignOutUser"},"status":"Pending","errorMessage":null},{"@c":".CloseFounderWalletCloseAccountStep","walletId":{"value":""},"steps":[{"type":{"value":"IgnoreRecurrences"},"status":"Pending","errorMessage":null},{"type":{"value":"DeleteWalletPixKey"},"status":"Pending","errorMessage":null}],"status":"Pending","errorMessage":null,"type":{"value":"CloseFounderWallet"}}],"tryCount":0}"""
        val data = parseObjectFromCloseAccount<CloseAccountMessage>(message)
        data.steps.map {
            it.type.value
        }.shouldContainExactly("Unsubscribe", "RemoveFromDDA", "SignOutUser", "CloseFounderWallet")

        (data.steps[3] as CloseFounderWalletCloseAccountStep).steps.map { it.type.value }.shouldContainExactly("IgnoreRecurrences", "DeleteWalletPixKey")
    }
/*
    @Test
    fun `deve gerar uma string`() {
        val payload = CloseAccountMessage(
            accountId = AccountId(ACCOUNT_ID),
            steps = listOf(
                CloseAccountStepTypeUnsubscribe.toSimpleStep(),
                CloseAccountStepTypeRemoveFromDDA.toSimpleStep(),
                CloseAccountStepTypeSignOutUser.toSimpleStep(),
                CloseFounderWalletCloseAccountStep(
                    walletId = WalletId(value = ""),
                    steps = listOf(
                        CloseWalletStepTypeIgnoreRecurrences.toStep(),
                        CloseWalletStepTypeDeleteWalletPixKey.toStep()
                    ),
                    status = CloseAccountStepStatus.Pending,
                    errorMessage = null
                )
            ),
            tryCount = 0
        )

        println(getObjectMapper().writeValueAsString(payload))
    }
*/
}