package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.fee.BillFees
import ai.friday.billpayment.app.fee.FeePaymentCreditCardConvenience
import ai.friday.billpayment.app.fee.FeePaymentCreditCardInstallment
import ai.friday.billpayment.app.fee.FeePaymentMethodCreditCard
import ai.friday.billpayment.app.fee.FeesWithSummary
import ai.friday.billpayment.app.integrations.ListFeesService
import ai.friday.billpayment.fixture.ListFeeRequestTOFixture
import ai.friday.billpayment.integration.WalletFixture
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail

class FeeControllerTest {
    private val listFeesService: ListFeesService = mockk()
    private val test = FeeController(listFeesService)

    private val auth: Authentication = mockk {
        every { name } returns "test"
        every { attributes } returns mapOf("WALLET" to WalletFixture().buildWallet())
    }

    @Test
    fun `should return 200 when list fees`() {
        val feePaymentMethod = FeePaymentMethodCreditCard(
            amount = 27195,
            paymentMethodId = AccountPaymentMethodId(value = "777"),
            convenience = FeePaymentCreditCardConvenience(
                fee = 499,
                totalValue = 28552,
                totalValueRounded = 28552,
                totalInterestValue = 1357,
                totalInterestValueRounded = 1357,
            ),
            installments = listOf(
                FeePaymentCreditCardInstallment(28552, 28552, 1, 0.0, 0),
                FeePaymentCreditCardInstallment(15245, 30489, 2, 4.49, 1937),
            ),
        )

        every {
            listFeesService.listFees(any())
        } returns FeesWithSummary(
            fees = listOf(
                BillFees(
                    billId = "",
                    amount = null,
                    calculationId = null,
                    payments = listOf(feePaymentMethod),
                    error = null,
                ),
            ),
            summary = null,
        ).right()

        val request = ListFeeRequestTOFixture.create(
            bills = arrayOf(
                ListFeeRequestTOFixture.bill(
                    methods = listOf(ListFeeRequestTOFixture.cc(paymentMethodId = feePaymentMethod.paymentMethodId.value)),
                ),
            ),
        )

        val result = test.listFees(auth, request)

        result.status shouldBe HttpStatus.OK
        result.body().shouldBeTypeOf<FeesWithSummaryTO>()

        with(result.body() as FeesWithSummaryTO) {
            fees.size shouldBe 1
            with(fees[0]) {
                payments.size shouldBe 1
                with(payments[0]) {
                    type shouldBe feePaymentMethod.method
                    amount shouldBe feePaymentMethod.amount
                    paymentMethodId shouldBe feePaymentMethod.paymentMethodId.value
                    convenience?.fee shouldBe feePaymentMethod.convenience?.fee
                    convenience?.totalValue shouldBe feePaymentMethod.convenience?.totalValue
                    convenience?.totalValueRounded shouldBe feePaymentMethod.convenience?.totalValueRounded
                    convenience?.totalInterestValue shouldBe feePaymentMethod.convenience?.totalInterestValue
                    convenience?.totalInterestValueRounded shouldBe feePaymentMethod.convenience?.totalInterestValueRounded

                    installments?.size shouldBe 2
                    // Kotlin: Smart cast to 'List<FeePaymentCreditCardInstallmentTO>' is impossible, because 'installments' is a public API property declared in different module
                    val fn: (List<FeePaymentCreditCardInstallmentTO>?) -> Unit = { v ->
                        if (v == null) fail("installments is null")

                        v[0] shouldBe FeePaymentCreditCardInstallmentTO(28552, 28552, 1, 0, 0)
                        v[1] shouldBe FeePaymentCreditCardInstallmentTO(15245, 30489, 2, 449, 1937)
                    }

                    fn(installments)
                }
            }
        }
    }
}