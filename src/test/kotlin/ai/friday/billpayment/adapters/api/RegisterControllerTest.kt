package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.accountRegisterData
import ai.friday.billpayment.accountRegisterDataWithAddress
import ai.friday.billpayment.accountRegisterDataWithDocumentInfo
import ai.friday.billpayment.accountRegisterDataWithPhoneVerified
import ai.friday.billpayment.accountRegisterMissingOnlyAgreement
import ai.friday.billpayment.accountRegisterWithEmailNotVerified
import ai.friday.billpayment.adapters.s3.S3LinkGenerator
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AgreementData
import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.SecurityFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.right
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.longs.shouldBeInRange
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.PropertySource
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

const val agreementFileUrl = "https://s3.file/termos_de_uso.pdf"

@MicronautTest(environments = [FRIDAY_ENV])
@PropertySource(
    value = [
        Property(name = "urls.termsOfUse", value = agreementFileUrl),
    ],
)
class RegisterControllerTest(embeddedServer: EmbeddedServer) {
    private val securityFixture = SecurityFixture()

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val userContractFileUrl = "https://api.via1.app/test/register/userContract"

    @MockBean(RegisterService::class)
    fun getRegisterService() = registerService
    private val registerService: RegisterService = mockk() {
        every {
            processLiveness(any())
        } answers {
            firstArg<AccountRegisterData>().right()
        }
    }

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(AccountService::class)
    fun accountService() = accountService
    private val accountService: AccountService = mockk()

    @MockBean(SystemActivityService::class)
    fun getSystemActivityService() = systemActivityService
    private val systemActivityService: SystemActivityService = mockk()

    @MockBean(S3LinkGenerator::class)
    fun getS3LinkGenerator() = s3LinkGenerator
    private val s3LinkGenerator: S3LinkGenerator = mockk()

    private fun buildGetAccountRegisterRequest(cookie: Cookie): MutableHttpRequest<Unit> {
        return HttpRequest.GET<Unit>("/register")
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private fun buildUpgradeGetAccountRegisterRequest(cookie: Cookie): MutableHttpRequest<Unit> {
        return HttpRequest.GET<Unit>("/upgrade")
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private fun buildPutFlowRequest(cookie: Cookie, flow: String): MutableHttpRequest<String> {
        return HttpRequest.PUT("/register/flow/$flow", "")
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    @Test
    fun `should return mobile phone and expiration in get register when user has a phone to validate`() {
        val request = buildGetAccountRegisterRequest(securityFixture.cookieGuest)
        val expiration = getZonedDateTime().plusMinutes(1).toEpochSecond()

        val accountRegisterData = accountRegisterData.copy(
            mobilePhone = MobilePhone("+*************"),
            mobilePhoneVerified = false,
            mobilePhoneTokenExpiration = expiration,
        )
        every { registerService.findByAccountId(any()) } returns Either.Right(accountRegisterData)
        every { registerService.updateAgreementFiles(any(), any()) } returns accountRegisterData.agreementData

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            assertSoftly {
                mobilePhone?.number shouldBe "+*************"
                mobilePhone?.isVerified shouldBe false
                mobilePhone?.tokenExpiration!! shouldBeInRange LongRange(
                    expiration - getZonedDateTime().toEpochSecond(),
                    60,
                )
            }
        }
    }

    @Test
    fun `should not return mobile phone and expiration in get register when mobile token expiration is null`() {
        val request = buildGetAccountRegisterRequest(securityFixture.cookieGuest)

        val accountRegisterData = accountRegisterData.copy(
            mobilePhone = MobilePhone("+*************"),
            mobilePhoneVerified = false,
            mobilePhoneTokenExpiration = null,
        )
        every { registerService.findByAccountId(any()) } returns Either.Right(accountRegisterData)
        every { registerService.updateAgreementFiles(any(), any()) } returns accountRegisterData.agreementData

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            mobilePhone.shouldBeNull()
        }
    }

    @Test
    fun `should return account register when user is guest and a valid document is uploaded`() {
        val request = buildGetAccountRegisterRequest(securityFixture.cookieGuest)

        every { registerService.findByAccountId(any()) } returns Either.Right(accountRegisterDataWithDocumentInfo)
        every {
            registerService.updateAgreementFiles(
                any(),
                any(),
            )
        } returns accountRegisterDataWithDocumentInfo.agreementData

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            assertSoftly {
                document.cpf shouldBe accountRegisterDataWithDocumentInfo.documentInfo!!.cpf
                document.birthState shouldBe accountRegisterDataWithDocumentInfo.documentInfo?.birthState
                document.birthCity shouldBe accountRegisterDataWithDocumentInfo.documentInfo?.birthCity
                document.orgEmission shouldBe accountRegisterDataWithDocumentInfo.documentInfo?.orgEmission
                document.expeditionDate shouldBe accountRegisterDataWithDocumentInfo.documentInfo?.expeditionDate!!.format(
                    DateTimeFormatter.ofPattern("dd/MM/yyyy"),
                )
            }
        }
    }

    @Test
    fun `deve retornar o account register para um usuário ativo`() {
        val request = buildUpgradeGetAccountRegisterRequest(securityFixture.cookieAuthOwner)

        every { registerService.findByAccountId(any()) } returns Either.Right(accountRegisterDataWithDocumentInfo)
        every {
            registerService.updateAgreementFiles(
                any(),
                any(),
            )
        } returns accountRegisterDataWithDocumentInfo.agreementData
        every { registerService.startUpgrade(any()) } returns Unit

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        verify { registerService.startUpgrade(any()) }

        response.status shouldBe HttpStatus.OK
    }

    @Test
    fun `deve retornar um account register de um usuário sem email verificado`() {
        val request = buildGetAccountRegisterRequest(securityFixture.cookieMsisdnAuth)

        every { registerService.findByAccountId(any()) } returns Either.Right(accountRegisterWithEmailNotVerified)
        every {
            registerService.updateAgreementFiles(
                any(),
                any(),
            )
        } returns accountRegisterWithEmailNotVerified.agreementData

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            assertSoftly {
                email shouldBe null
                emailVerification shouldBe null
            }
        }
    }

    @Test
    fun `should return account register when user is guest and missing only agreement`() {
        val request = buildGetAccountRegisterRequest(securityFixture.cookieGuest)

        every { registerService.findByAccountId(any()) } returns Either.Right(
            accountRegisterMissingOnlyAgreement.copy(
                openForUserReview = true,
            ),
        )
        every { registerService.updateAgreementFiles(any(), any()) } returns AgreementData(
            acceptedAt = null,
            userContractFile = StoredObject("", "", ""),
            userContractSignature = "FAKE_SIGNATURE",
            declarationOfResidencyFile = StoredObject("", "", ""),
        )
        every { s3LinkGenerator.generate(any(), any(), any()) } returns userContractFileUrl

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            document.cpf shouldBe accountRegisterDataWithDocumentInfo.documentInfo!!.cpf
            document.birthState shouldBe accountRegisterDataWithDocumentInfo.documentInfo?.birthState
            document.birthCity shouldBe accountRegisterDataWithDocumentInfo.documentInfo?.birthCity
            userContract?.hasAccepted shouldBe false
            userContract?.url shouldBe userContractFileUrl
            agreement shouldBe agreementFileUrl
            openForUserReview shouldBe true
        }
    }

    @Test
    fun `should return account without valid uploaded document`() {
        val request = buildGetAccountRegisterRequest(securityFixture.cookieGuest)

        every { registerService.findByAccountId(any()) } returns Either.Right(accountRegisterDataWithPhoneVerified)
        every {
            registerService.updateAgreementFiles(
                any(),
                any(),
            )
        } returns accountRegisterDataWithPhoneVerified.agreementData

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            mobilePhone?.number shouldBe accountRegisterDataWithDocumentInfo.mobilePhone!!.msisdn
            mobilePhone?.isVerified shouldBe accountRegisterDataWithDocumentInfo.mobilePhoneVerified
            accountId shouldBe accountRegisterDataWithDocumentInfo.accountId.value
        }
    }

    @Test
    fun `should return internal server error on service left return`() {
        val request = buildGetAccountRegisterRequest(securityFixture.cookieGuest)

        every { registerService.findByAccountId(any()) } returns Either.Left(ItemNotFoundException(""))

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
    }

    @Test
    fun `should not send contract url when account register data is not ready`() {
        val request = buildGetAccountRegisterRequest(securityFixture.cookieGuest)

        every { registerService.findByAccountId(any()) } returns Either.Right(accountRegisterDataWithAddress)
        every {
            registerService.updateAgreementFiles(
                any(),
                any(),
            )
        } returns accountRegisterDataWithAddress.agreementData

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            userContract?.url shouldBe null
        }
        verify(exactly = 0) { s3LinkGenerator.generate(any(), any(), any()) }
    }

    @Test
    fun `should send contract and declaration of residency urls when account register data is ready`() {
        val request = buildGetAccountRegisterRequest(securityFixture.cookieGuest)

        val contractFile = StoredObject("", "", "contract")
        val expectedContractLink = "contractLinkGenerated"

        val declarationOfResidencyFile = StoredObject("", "", "residency")
        val expectedDeclarationOfResidencyLink = "residencyLinkGenerated"

        every { registerService.findByAccountId(any()) } returns Either.Right(accountRegisterMissingOnlyAgreement)

        every { registerService.updateAgreementFiles(any(), any()) } returns AgreementData(
            acceptedAt = null,
            userContractFile = contractFile,
            userContractSignature = "FAKE_KEY",
            declarationOfResidencyFile = declarationOfResidencyFile,
        )

        every { s3LinkGenerator.generate(contractFile, any(), any()) } returns expectedContractLink
        every {
            s3LinkGenerator.generate(
                declarationOfResidencyFile,
                any(),
                any(),
            )
        } returns expectedDeclarationOfResidencyLink

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountRegisterDataTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            userContract?.url shouldBe expectedContractLink
            userContract?.contract shouldBe expectedContractLink
            userContract?.declarationOfResidency shouldBe expectedDeclarationOfResidencyLink
        }
    }

    @Test
    fun `deve retornar no content quando adicionar um grupo`() {
        val request = buildPutFlowRequest(securityFixture.cookieGuest, RegisterFlow.BILLING_CAROUSEL.name)

        every { accountService.addGroups(any(), any()) } returns ACCOUNT.configuration.groups
        every { systemActivityService.setAcceptedBillingCarousel(any()) } just Runs

        val response = client.toBlocking()
            .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        verify {
            accountService.addGroups(
                AccountId(ACCOUNT_ID),
                listOf(AccountGroup.SUBSCRIPTION_STANDARD_PLAN),
            )
            systemActivityService.setAcceptedBillingCarousel(AccountId(ACCOUNT_ID))
        }
    }

    @Test
    fun `deve retornar bad request quando tentar adicionar um grupo não existente`() {
        val request = buildPutFlowRequest(securityFixture.cookieGuest, "alpha")

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `deve retornar internal server error quando falhar ao tentar salvar o grupo`() {
        val request = buildPutFlowRequest(securityFixture.cookieGuest, RegisterFlow.BILLING_CAROUSEL.name)

        every { accountService.addGroupsToAccount(any<AccountId>(), any()) } throws NoStackTraceException("")

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
    }

    @Test
    fun `deve retornar internal server error quando falhar em iniciar o upgrade`() {
        val request = buildUpgradeGetAccountRegisterRequest(securityFixture.cookieAuthOwner)

        every { registerService.startUpgrade(any()) } throws Exception("falha ao salvar no banco")

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
    }
}