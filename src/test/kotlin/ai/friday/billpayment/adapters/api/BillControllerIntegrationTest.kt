package ai.friday.billpayment.adapters.api

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DEFAULT_LEGACY_ACCOUNT_CONFIGURATION
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.RefundedBillDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SettlementFundsTransferDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.adapters.intercom.IntercomAdapter
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BillRegisterData
import ai.friday.billpayment.app.payment.BillRegisterStatus
import ai.friday.billpayment.app.payment.DefaultBillValidationResponse
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.onWallet
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.assertions.withClue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpMethod
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.DefaultHttpClientConfiguration
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.time.Duration
import java.time.LocalDate
import java.util.Optional
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV])
class BillControllerIntegrationTest(val embeddedServer: EmbeddedServer, private val amazonDynamoDb: AmazonDynamoDB) {

    val _client = RxHttpClient.create(
        embeddedServer.url,
        DefaultHttpClientConfiguration().apply {
            this.setReadTimeout(Duration.ofSeconds(5))
        },
    )

    private val enhancedClient = DynamoDBUtils.setupDynamoDB()

    private val billRepository = DynamoDbBillRepository(
        billClient = BillDynamoDAO(enhancedClient),
        refundedClient = RefundedBillDynamoDAO(enhancedClient),
        settlementFundsTransferClient = SettlementFundsTransferDynamoDAO(enhancedClient),
    )
    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val billEventRepository = BillEventDBRepository(
        billEventDAO = billEventDAO,
        uniqueConstraintDAO = uniqueConstraintDAO,
        featureConfiguration = mockk(relaxed = true),
        transactionDynamo = transactionDynamo,
    )

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = accountRepository,
    )

    private val walletFixture = WalletFixture()

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    val accountOfUnknownPerson = ACCOUNT.copy(
        accountId = AccountId("ACCOUNT-${UUID.randomUUID()}"),
        document = "***********",
        name = "Desconhecido de Tal",
        emailAddress = EmailAddress("<EMAIL>"),
        configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION.copy(
            externalId = ExternalId(value = "Desconhecido de Tal", providerName = AccountProviderName.ME_POUPE),
        ),
    )
    val someoneThatHasNothingToDoHereWallet = walletFixture.buildPrimaryWallet(accountOfUnknownPerson)

    val testCases = listOf(
        walletFixture.founder to HttpStatus.OK,
        walletFixture.participant to HttpStatus.OK,
        walletFixture.ultraLimitedParticipant to HttpStatus.FORBIDDEN,
        someoneThatHasNothingToDoHereWallet.founder to HttpStatus.FORBIDDEN,
    )

    val noContentTestCases = listOf(
        walletFixture.founder to HttpStatus.NO_CONTENT,
        walletFixture.participant to HttpStatus.NO_CONTENT,
        walletFixture.ultraLimitedParticipant to HttpStatus.FORBIDDEN,
        someoneThatHasNothingToDoHereWallet.founder to HttpStatus.FORBIDDEN,
    )

    val testBillAdded = billAdded.copy(amountCalculationModel = ai.friday.billpayment.app.payment.AmountCalculationModel.BENEFICIARY_ONLY)
    val bill = Bill.build(testBillAdded)
    val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant), id = bill.walletId)

    @field:Property(name = "lock.tableName")
    lateinit var lockTableName: String

    @BeforeEach
    fun setup() {
        embeddedServer.applicationContext.registerSingleton(_client)
        embeddedServer.applicationContext.registerSingleton(IntercomAdapter::class.java, mockk<IntercomAdapter>(relaxed = true))
        embeddedServer.applicationContext.registerSingleton(CrmRepository::class.java, mockk<CrmRepository>(relaxed = true))
        embeddedServer.applicationContext.registerSingleton(
            BoletoSettlementService::class.java,
            mockk<BoletoSettlementService> {
                every { validateBill(bill) } returns DefaultBillValidationResponse(
                    billRegisterData = BillRegisterData(
                        billType = BillType.CONCESSIONARIA,
                        assignor = "himenaeos",
                        recipient = null, recipientChain = null, payerDocument = null,
                        amount = 6579,
                        discount = 3882,
                        interest = 5106,
                        fine = 1399,
                        amountTotal = 8551,
                        expirationDate = LocalDate.now(),
                        dueDate = LocalDate.now(),
                        paymentLimitTime = "20:00",
                        settleDate = LocalDate.now(),
                        fichaCompensacaoType = null,
                        payerName = null,
                        amountCalculationModel = AmountCalculationModel.ON_DEMAND,
                        interestData = null, fineData = null, discountData = null, abatement = null,
                        amountPaid = 10,
                        paidDate = LocalDate.now(),
                        idNumber = null, registrationUpdateNumber = null, barCode = null, rebate = null, amountCalculationValidUntil = null,
                    ),
                    gateway = FinancialServiceGateway.ORIGINAL,
                    errorDescription = null,
                    billRegisterStatus = BillRegisterStatus(
                        notPayable = false,
                        alreadyPaid = false,
                        barcodeNotFound = false,
                    ),
                    retryable = false,
                )
            },
        )

        createBillPaymentTable(amazonDynamoDb)
        createBillEventTable(amazonDynamoDb)
        createLockTable(amazonDynamoDb, lockTableName)

        loadAccountIntoDb(amazonDynamoDb, accountId = walletFixture.founder.accountId)
        walletRepository.save(wallet)
        billRepository.save(bill)
        billEventRepository.save(testBillAdded)
    }

    @Test
    fun `deve permitir que o usuário compartilhando a carteira marque como pago`() {
        testCases.forEach { tc ->
            withClue("quando é o membro ${tc.first.name} executando a operação") {
                val uri = "/bill/id/${bill.billId.value}/mark-as-paid"
                val response = call<Unit, BillTO>(
                    HttpMethod.PUT,
                    uri,
                    null,
                    member = tc.first,
                )

                response shouldNotBe null

                response!!.status() shouldBe tc.second
                if (response.status() >= HttpStatus.TEMPORARY_REDIRECT) {
                    return
                }
                val body = response.body.get()

                body shouldNotBe null
                body.markedAsPaidBy shouldBe tc.first.accountId.value

                call<Unit, BillTO>(
                    HttpMethod.PUT,
                    "/bill/id/${bill.billId.value}/cancel-marked-as-paid",
                    null,
                    member = tc.first,
                )
            }
        }
    }

    @Test
    fun `deve permitir que o usuário compartilhando a carteira desmarque como pago`() {
        testCases.forEach { tc ->
            withClue("quando é o membro ${tc.first.name} executando a operação") {
                call<Unit, BillTO>(
                    HttpMethod.PUT,
                    "/bill/id/${bill.billId.value}/mark-as-paid",
                    null,
                    member = tc.first,
                )
                val response = call<Unit, BillTO>(
                    HttpMethod.PUT,
                    "/bill/id/${bill.billId.value}/cancel-marked-as-paid",
                    null,
                    member = tc.first,
                )

                response!!.status() shouldBe tc.second
                if (response.status() >= HttpStatus.TEMPORARY_REDIRECT) {
                    return
                }

                response shouldNotBe null

                val body = response.body.get()

                body shouldNotBe null
                body.markedAsPaidBy shouldBe null
            }
        }
    }

    @Test
    fun `deve permitir que o usuário compartilhando a carteira atualize a description`() {
        testCases.forEach { tc ->
            withClue("quando é o membro ${tc.first.name} executando a operação") {
                val response = call<Map<String, String>, BillTO>(
                    HttpMethod.PUT,
                    "/bill/id/${bill.billId.value}/description",
                    mapOf("description" to "Uma nova descrição!"),
                    member = tc.first,
                )

                response shouldNotBe null

                response!!.status() shouldBe tc.second
                if (response.status() >= HttpStatus.TEMPORARY_REDIRECT) {
                    return
                }

                response.body shouldBe Optional.empty()

                val updatedBill = billRepository.findBill(bill.billId, wallet.id)

                updatedBill shouldNotBe null
                updatedBill.billDescription shouldBe "Uma nova descrição!"
            }
        }
    }

    @Test
    fun `deve permitir que o usuário compartilhando a carteira atualize o valor da bill`() {
        noContentTestCases.forEach { tc ->
            withClue("quando é o membro ${tc.first.name} executando a operação") {
                val bankTransferEvent = billAdded.copy(billType = BillType.PIX)
                val bankTransferBill = Bill.build(bankTransferEvent)
                billRepository.save(bankTransferBill)
                billEventRepository.save(bankTransferEvent)
                val response = call<Map<String, Long>, BillTO>(
                    HttpMethod.PUT,
                    "/bill/id/${bill.billId.value}/amount",
                    mapOf("amount" to 9876L),
                    member = tc.first,
                )

                response shouldNotBe null

                response!!.status() shouldBe tc.second
                if (response.status() >= HttpStatus.TEMPORARY_REDIRECT) {
                    return
                }

                response.body shouldBe Optional.empty()

                val updatedBill = billRepository.findBill(bill.billId, wallet.id)

                updatedBill shouldNotBe null
                updatedBill.amount shouldBe 9876
            }
        }
    }

    @Test
    fun `deve permitir que o usuário compartilhando a carteira ignore a bill`() {
        testCases.forEach { tc ->
            withClue("quando é o membro ${tc.first.name} executando a operação") {
                val response = call<Unit, BillTO>(
                    HttpMethod.PUT,
                    "/bill/id/${bill.billId.value}/ignore",
                    null,
                    member = tc.first,
                )

                response shouldNotBe null

                response!!.status() shouldBe tc.second
                if (response.status() >= HttpStatus.TEMPORARY_REDIRECT) {
                    return
                }

                response.body shouldBe Optional.empty()

                val updatedBill = billRepository.findBill(bill.billId, wallet.id)

                updatedBill shouldNotBe null
                updatedBill.status shouldBe BillStatus.IGNORED
            }
        }
    }

    @Test
    fun `deve permitir que o usuário compartilhando a carteira consiga reativar a bill`() {
        testCases.forEach { tc ->
            withClue("quando é o membro ${tc.first.name} executando a operação") {
                val response = call<Unit, BillTO>(
                    HttpMethod.PUT,
                    "/bill/id/${bill.billId.value}/ignore",
                    null,
                    member = tc.first,
                )
                response shouldNotBe null
                response!!.status() shouldBe tc.second
                if (response.status() >= HttpStatus.TEMPORARY_REDIRECT) {
                    return
                }
                response.body shouldBe Optional.empty()

                val ignoredBill = billRepository.findBill(bill.billId, wallet.id)

                ignoredBill.status shouldBe BillStatus.IGNORED

                val reactivateResponse = call<Unit, BillTO>(
                    HttpMethod.PUT,
                    "/bill/id/${bill.billId.value}/reactivate",
                    null,
                    member = tc.first,
                )
                reactivateResponse!!.status() shouldBe HttpStatus.OK
                reactivateResponse.body shouldNotBe null

                val updatedBill = billRepository.findBill(bill.billId, wallet.id)

                updatedBill shouldNotBe null
                updatedBill.status shouldBe BillStatus.ACTIVE
            }
        }
    }

    private inline fun <T, reified R> call(httpMethod: HttpMethod, uri: String, body: T?, member: Member): HttpResponse<R>? {
        val httpRequest = HttpRequest.create<T>(httpMethod, uri).onWallet(wallet = wallet, member = member)
        body?.let { httpRequest.body(it) }
        try {
            val response = _client.toBlocking().exchange(httpRequest, Argument.of(R::class.java))
            return response
        } catch (e: Exception) {
            if (e is HttpClientResponseException) {
                return e.response as HttpResponse<R>?
            }
            throw e
        }
    }
}