package ai.friday.billpayment.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.InviteStatus
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.generateCookie
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import io.mockk.spyk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

@MicronautTest(environments = [FRIDAY_ENV])
internal class PublicInvitesControllerTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    private val enhancedClient = getDynamoDB()

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(WalletRepository::class)
    fun walletRepository(): WalletRepository = walletRepository

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository: WalletRepository = spyk(
        WalletDbRepository(
            walletDAO = walletDAO,
            walletMemberDAO = walletMemberDAO,
            inviteDAO = inviteDAO,
            inviteReminderDAO = inviteReminderDAO,
            walletLimitDAO = walletLimitDAO,
            accountRepository = mockk(),
        ),
    )

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val accountId = wallet.founder.accountId
    private val walletId = wallet.id
    private val founder = wallet.founder

    private fun buildRequest(
        cookie: Cookie,
        walletId: WalletId,
        document: String,
    ): MutableHttpRequest<InviteRequestTO> {
        return HttpRequest.GET<InviteRequestTO>("/public/invite/${walletId.value}/$document")
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private fun buildAnonymousRequest(walletId: WalletId, document: String): MutableHttpRequest<InviteRequestTO> {
        return HttpRequest.GET<InviteRequestTO>("/public/invite/${walletId.value}/$document")
            .header("X-API-VERSION", "2")
    }

    private fun buildPrivateRequest(cookie: Cookie, walletId: WalletId): MutableHttpRequest<InviteRequestTO> {
        return HttpRequest.GET<InviteRequestTO>("/wallet/${walletId.value}/invite")
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `should return not found when walletID does not exists`() {
        val request = buildAnonymousRequest(walletId, DOCUMENT)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.listOf(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe InviteErrorsResponse.InviteNotFound.code
    }

    @Test
    fun `should return not found when there is no invite for document`() {
        val invite = Invite(
            walletId = walletId,
            memberDocument = DOCUMENT_2,
            memberName = "Alexandre",
            memberType = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                viewBalance = true,
                notification = true,
            ),
            status = InviteStatus.PENDING,
            validUntil = getLocalDate(),
            walletName = wallet.name,
            founderName = founder.name,
            founderDocument = founder.document,
            created = getZonedDateTime(),
        )

        walletRepository.save(invite)

        val request = buildAnonymousRequest(walletId, DOCUMENT)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe InviteErrorsResponse.InviteNotFound.code
    }

    @ParameterizedTest
    @EnumSource(InviteStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PENDING"])
    fun `should return gone when invite is not pending`(inviteStatus: InviteStatus) {
        val invite = Invite(
            walletId = walletId,
            memberDocument = DOCUMENT,
            memberName = "Alexandre",
            memberType = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                viewBalance = true,
                notification = true,
            ),
            status = inviteStatus,
            validUntil = getLocalDate(),
            walletName = wallet.name,
            founderName = founder.name,
            founderDocument = founder.document,
            created = getZonedDateTime(),
        )

        walletRepository.save(invite)

        val request = buildAnonymousRequest(walletId, DOCUMENT)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.GONE
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe InviteErrorsResponse.InviteNotPending.code
    }

    @Test
    fun `should return invite for anonymous user`() {
        val invite = Invite(
            walletId = walletId,
            memberDocument = DOCUMENT,
            memberName = "Alexandre",
            memberType = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                viewBalance = true,
                notification = true,
            ),
            status = InviteStatus.PENDING,
            validUntil = getLocalDate(),
            walletName = wallet.name,
            founderName = founder.name,
            founderDocument = founder.document,
            created = getZonedDateTime(),
        )

        walletRepository.save(invite)

        val request = buildAnonymousRequest(walletId, DOCUMENT)

        val response = client.toBlocking()
            .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            this.walletId shouldBe invite.walletId.value
            this.memberDocument shouldBe invite.memberDocument
            this.memberName shouldBe invite.memberName
            this.status shouldBe invite.status.name
            this.permissions.scheduleBills shouldBe invite.permissions.scheduleBills
            this.permissions.viewBills shouldBe invite.permissions.viewBills
        }
    }

    @ParameterizedTest
    @EnumSource(Role::class)
    fun `should return invite for authenticated user`(role: Role) {
        val invite = Invite(
            walletId = walletId,
            memberDocument = DOCUMENT,
            memberName = "Alexandre",
            memberType = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                viewBalance = true,
                notification = true,
            ),
            status = InviteStatus.PENDING,
            validUntil = getLocalDate(),
            walletName = wallet.name,
            founderName = founder.name,
            founderDocument = founder.document,
            created = getZonedDateTime(),
        )

        walletRepository.save(invite)

        val request = buildRequest(generateCookie(accountId, role), walletId, DOCUMENT)

        val response = client.toBlocking()
            .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            this.walletId shouldBe invite.walletId.value
            this.memberDocument shouldBe invite.memberDocument
            this.memberName shouldBe invite.memberName
            this.status shouldBe invite.status.name
            this.permissions.scheduleBills shouldBe invite.permissions.scheduleBills
            this.permissions.viewBills shouldBe invite.permissions.viewBills
        }
    }

    @Test
    fun `should return not found when walletID does not exists on private request`() {
        loadAccountIntoDb(dynamoDB, accountId = accountId)
        val request = buildPrivateRequest(generateCookie(accountId, Role.OWNER), walletId)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.listOf(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe InviteErrorsResponse.InviteNotFound.code
    }

    @Test
    fun `should return not found when there is no invite for current user`() {
        val invite = Invite(
            walletId = walletId,
            memberDocument = DOCUMENT_2,
            memberName = "Alexandre",
            memberType = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                viewBalance = true,
                notification = true,
            ),
            status = InviteStatus.PENDING,
            validUntil = getLocalDate(),
            walletName = wallet.name,
            founderName = founder.name,
            founderDocument = founder.document,
            created = getZonedDateTime(),
        )

        walletRepository.save(invite)

        loadAccountIntoDb(dynamoDB, accountId = accountId)
        val request = buildPrivateRequest(generateCookie(accountId, Role.OWNER), walletId)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe InviteErrorsResponse.InviteNotFound.code
    }

    @ParameterizedTest
    @EnumSource(InviteStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PENDING"])
    fun `should return gone when invite is not pending for current user`(inviteStatus: InviteStatus) {
        val invite = Invite(
            walletId = walletId,
            memberDocument = DOCUMENT,
            memberName = "Alexandre",
            memberType = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                viewBalance = true,
                notification = true,
            ),
            status = inviteStatus,
            validUntil = getLocalDate(),
            walletName = wallet.name,
            founderName = founder.name,
            founderDocument = founder.document,
            created = getZonedDateTime(),
        )

        walletRepository.save(invite)

        loadAccountIntoDb(dynamoDB, accountId = accountId)
        val request = buildPrivateRequest(generateCookie(accountId, Role.OWNER), walletId)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.GONE
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe InviteErrorsResponse.InviteNotPending.code
    }

    @Test
    fun `should return invite for authenticated user on private request`() {
        val invite = Invite(
            walletId = walletId,
            memberDocument = DOCUMENT,
            memberName = "Alexandre",
            memberType = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                viewBalance = true,
                notification = true,
            ),
            status = InviteStatus.PENDING,
            validUntil = getLocalDate(),
            walletName = wallet.name,
            founderName = founder.name,
            founderDocument = founder.document,
            created = getZonedDateTime(),
        )

        walletRepository.save(invite)

        loadAccountIntoDb(dynamoDB, accountId = accountId)
        val request = buildPrivateRequest(generateCookie(accountId, Role.OWNER), walletId)

        val response = client.toBlocking()
            .exchange(request, Argument.of(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body.get()) {
            this.walletId shouldBe invite.walletId.value
            this.memberDocument shouldBe invite.memberDocument
            this.memberName shouldBe invite.memberName
            this.status shouldBe invite.status.name
            this.permissions.scheduleBills shouldBe invite.permissions.scheduleBills
            this.permissions.viewBills shouldBe invite.permissions.viewBills
        }
    }
}