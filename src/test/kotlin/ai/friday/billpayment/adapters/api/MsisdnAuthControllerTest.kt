package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.InvalidTokenException
import ai.friday.billpayment.app.account.InvalidTokenReason
import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.TokenChannel
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.msisdnauth.IssueTokenError
import ai.friday.billpayment.app.msisdnauth.IssuedToken
import ai.friday.billpayment.app.msisdnauth.MsisdnAuthId
import ai.friday.billpayment.app.msisdnauth.MsisdnAuthService
import ai.friday.billpayment.app.msisdnauth.ValidateTokenRequest
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.toSignedCookie
import ai.friday.billpayment.integration.withAudience
import ai.friday.billpayment.integration.withClaim
import ai.friday.billpayment.integration.withExpiresAt
import ai.friday.billpayment.integration.withIssuedAt
import ai.friday.billpayment.integration.withIssuer
import ai.friday.billpayment.integration.withSubject
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.nimbusds.jwt.JWTClaimsSet
import com.nimbusds.jwt.JWTParser
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.Duration
import java.time.temporal.ChronoUnit
import java.util.Date
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
internal class MsisdnAuthControllerTest(
    embeddedServer: EmbeddedServer,
    private val msisdnAuthConfiguration: MsisdnAuthConfiguration,
    val dynamoDB: AmazonDynamoDB,
) {
    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val authenticationService: MsisdnAuthService = mockk()
    private val cognitoAdapterMock: UserPoolAdapter = mockk()
    private val accountService: AccountService = mockk()

    @MockBean(MsisdnAuthService::class)
    fun getAuthenticationService(): MsisdnAuthService = authenticationService

    @MockBean(RegisterService::class)
    fun getRegisterService(): RegisterService = mockk()

    @MockBean(AccountService::class)
    fun accountService(): AccountService = accountService

    @MockBean(UserPoolAdapter::class)
    fun cognitoAdapterMock(): UserPoolAdapter = cognitoAdapterMock

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    private val accountId = AccountId("ACCOUNT-123-456")
    private val otpId = MsisdnAuthId("MSISDN_AUTH-123-456")

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `deve retornar o otpId em caso de sucesso`() {
        every { authenticationService.issueToken(any(), any()) } returns (IssuedToken(otpId, duration = Duration.ofSeconds(10), cooldown = Duration.ofSeconds(5))).right()

        val requestTO = IssueTokenRequestTO(number = "+*************", channel = TokenChannel.SMS)

        val request = HttpRequest.POST("/public/msisdn/issueToken", requestTO).header("X-API-VERSION", "2")

        val response = client.toBlocking().exchange(
            request,
            Argument.of(IssueTokenResponseTO::class.java),
        )

        response.status shouldBe HttpStatus.CREATED
        response.body()!!.otpId shouldBe otpId.value
    }

    @Test
    fun `deve retornar INTERNAL_SERVER_ERROR em caso de erro emitindo o token`() {
        every { authenticationService.issueToken(any(), any()) } returns IssueTokenError.Error(NoStackTraceException())
            .left()

        val requestTO = IssueTokenRequestTO(number = "+*************", channel = TokenChannel.SMS)

        val request = HttpRequest.POST("/public/msisdn/issueToken", requestTO).header("X-API-VERSION", "2")

        val response = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                request,
                Argument.of(IssueTokenResponseTO::class.java),
                Argument.of(String::class.java),
            )
        }

        response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
    }

    @Test
    fun `deve retornar sucesso em caso de limite de token excedido`() {
        every { authenticationService.issueToken(any(), any()) } returns IssuedToken(otpId, Duration.ZERO, Duration.ZERO).right()

        val requestTO = IssueTokenRequestTO(number = "+*************", channel = TokenChannel.SMS)

        val request = HttpRequest.POST("/public/msisdn/issueToken", requestTO).header("X-API-VERSION", "2")

        val response =
            client.toBlocking().exchange(
                request,
                Argument.of(IssueTokenResponseTO::class.java),
                Argument.of(String::class.java),
            )

        response.status shouldBe HttpStatus.CREATED
        response.body()!!.otpId shouldBe otpId.value
        response.body()!!.duration shouldBe Duration.ZERO.seconds.toString()
    }

    @Test
    fun `deve retornar forbidden quando o DDI não é brasileiro`() {
        val request = HttpRequest.POST("/public/msisdn/issueToken", IssueTokenRequestTO(number = "229999", channel = TokenChannel.SMS)).header("X-API-VERSION", "2")
        val response = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.VOID, Argument.of(HttpResponse::class.java))
        }

        response.status shouldBe HttpStatus.FORBIDDEN

        verify(exactly = 0) {
            authenticationService.issueToken(any(), any())
        }
    }

    @ParameterizedTest
    @MethodSource("jwtUserRoles")
    fun `deve retornar um jwt válido em caso de sucesso e user com role OWNER`(
        role: Role,
        isGuest: Boolean,
        doesUserExist: Boolean,
        migrated: Boolean?,
    ) {
        val mobilePhone = MobilePhone("+*************")
        val slot = slot<ValidateTokenRequest>()
        every { cognitoAdapterMock.doesUserExist(any()) } returns doesUserExist
        every { accountService.isGuestAccount(any()) } returns isGuest
        every { accountService.findAccountById(any()) } returns ACCOUNT
        every { authenticationService.validateToken(capture(slot)) } returns (mobilePhone to accountId).right()
        val requestTO = ValidateTokenRequestTO(token = "1234", otpId = otpId.value)

        val request = HttpRequest.PUT("/public/msisdn/validateToken", requestTO).header("X-API-VERSION", "2")

        val dateTime = getZonedDateTime()

        val response = withGivenDateTime(dateTime) {
            client.toBlocking().exchange(request, Argument.of(ValidateTokenResponseTO::class.java))
        }

        response.status shouldBe HttpStatus.OK

        with(response.body()!!) {
            this.otpId shouldBe otpId
            val jwt = JWTParser.parse(this.jwt)

            jwt.jwtClaimsSet.subject shouldBe accountId.value
            jwt.jwtClaimsSet.issuer shouldBe msisdnAuthConfiguration.issuer
            jwt.jwtClaimsSet.audience.first() shouldBe msisdnAuthConfiguration.audience
            jwt.jwtClaimsSet.claims["actualRole"] shouldBe role.name
            jwt.jwtClaimsSet.claims["msisdn:migrated"] shouldBe migrated
            jwt.jwtClaimsSet.claims["email"] shouldBe "${mobilePhone.msisdn}@friday.ai"
            jwt.jwtClaimsSet.expirationTime.toInstant() shouldBe dateTime.plus(msisdnAuthConfiguration.duration)
                .truncatedTo(ChronoUnit.SECONDS)
                .toInstant()
            jwt.header.algorithm.name shouldBe "HS256"
        }

        with(slot.captured) {
            this.token shouldBe requestTO.token
            this.otpId shouldBe otpId
        }
    }

    @Test
    fun `deve retornar INTERNAL_SERVER_ERROR em caso de erro validando o token`() {
        every { authenticationService.validateToken(any()) } returns NoStackTraceException().left()

        val requestTO = ValidateTokenRequestTO(token = "1234", otpId = "accountId")

        val request = HttpRequest.PUT("/public/msisdn/validateToken", requestTO).header("X-API-VERSION", "2")

        val response = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                request,
                Argument.of(ValidateTokenResponseTO::class.java),
                Argument.of(String::class.java),
            )
        }

        response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
    }

    @Test
    fun `deve retornar BAD_REQUEST em caso de token invalido`() {
        every { authenticationService.validateToken(any()) } returns InvalidTokenException(reason = InvalidTokenReason.EXPIRED).left()

        val requestTO = ValidateTokenRequestTO(token = "1234", otpId = "accountId")

        val request = HttpRequest.PUT("/public/msisdn/validateToken", requestTO).header("X-API-VERSION", "2")

        val response = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(
                request,
                Argument.of(ValidateTokenResponseTO::class.java),
                Argument.of(String::class.java),
            )
        }

        response.status shouldBe HttpStatus.BAD_REQUEST
    }

    @ParameterizedTest
    @MethodSource("jwtUserRoles")
    fun `deve atualizar o jwt quando o token atual é valido`(
        role: Role,
        isGuest: Boolean,
        doesUserExist: Boolean,
        migrated: Boolean?,
    ) {
        val mobilePhone = MobilePhone("+*************")
        val slot = slot<ValidateTokenRequest>()
        every { cognitoAdapterMock.doesUserExist(any()) } returns doesUserExist
        every { accountService.isGuestAccount(any()) } returns isGuest
        every { accountService.findAccountById(any()) } returns ACCOUNT
        every { authenticationService.validateToken(capture(slot)) } returns (mobilePhone to accountId).right()

        val request = HttpRequest.GET<Unit>("/public/msisdn/refreshToken").header("X-API-VERSION", "2")
            .cookie(
                JWTClaimsSet.Builder()
                    .withSubject(accountId.value)
                    .withIssuer(msisdnAuthConfiguration.issuer)
                    .withAudience(msisdnAuthConfiguration.audience)
                    .withClaim("email", "${mobilePhone.msisdn}@friday.ai")
                    .withClaim("actualRole", "OWNER")
                    .withIssuedAt(Date(getZonedDateTime().minusHours(5).toInstant().toEpochMilli()))
                    .withExpiresAt(Date(getZonedDateTime().plusHours(5).toInstant().toEpochMilli()))
                    .toSignedCookie(),
            )

        val dateTime = getZonedDateTime()

        val response = withGivenDateTime(dateTime) {
            client.toBlocking().exchange(request, Argument.of(ValidateTokenResponseTO::class.java))
        }

        response.status shouldBe HttpStatus.OK

        with(response.body()!!) {
            this.otpId shouldBe "tokenRefreshed"
            val jwt = JWTParser.parse(this.jwt)

            jwt.jwtClaimsSet.subject shouldBe accountId.value
            jwt.jwtClaimsSet.issuer shouldBe msisdnAuthConfiguration.issuer
            jwt.jwtClaimsSet.audience.first() shouldBe msisdnAuthConfiguration.audience
            jwt.jwtClaimsSet.claims["actualRole"] shouldBe role.name
            jwt.jwtClaimsSet.claims["msisdn:migrated"] shouldBe migrated
            jwt.jwtClaimsSet.claims["email"] shouldBe "${mobilePhone.msisdn}@friday.ai"
            jwt.jwtClaimsSet.expirationTime.toInstant() shouldBe dateTime.plus(msisdnAuthConfiguration.duration)
                .truncatedTo(ChronoUnit.SECONDS)
                .toInstant()
            jwt.header.algorithm.name shouldBe "HS256"
        }
    }

    @Test
    fun `deve falhar a atualização quando o token JWT atual está expirado`() {
        val mobilePhone = MobilePhone("+*************")

        val request = HttpRequest.GET<Unit>("/public/msisdn/refreshToken").header("X-API-VERSION", "2")
            .cookie(
                JWTClaimsSet.Builder()
                    .withSubject(accountId.value)
                    .withIssuer(msisdnAuthConfiguration.issuer)
                    .withAudience(msisdnAuthConfiguration.audience)
                    .withClaim("email", "${mobilePhone.msisdn}@friday.ai")
                    .withIssuedAt(Date(getZonedDateTime().minusHours(1).toInstant().toEpochMilli()))
                    .withExpiresAt(Date(getZonedDateTime().minusSeconds(1).toInstant().toEpochMilli()))
                    .toSignedCookie(),
            )

        val dateTime = getZonedDateTime()

        val response = withGivenDateTime(dateTime) {
            assertThrows<HttpClientResponseException> {
                client.toBlocking().exchange(request, Argument.of(ValidateTokenResponseTO::class.java))
            }
        }

        response.status shouldBe HttpStatus.UNAUTHORIZED
    }

    @Test
    fun `deve falhar se a assinatura do token JWT atual estiver errada`() {
        val mobilePhone = MobilePhone("+*************")

        val request = HttpRequest.GET<Unit>("/public/msisdn/refreshToken").header("X-API-VERSION", "2")
            .cookie(
                JWTClaimsSet.Builder()
                    .withSubject(accountId.value)
                    .withIssuer(msisdnAuthConfiguration.issuer)
                    .withAudience(msisdnAuthConfiguration.audience)
                    .withClaim("email", "${mobilePhone.msisdn}@friday.ai")
                    .withIssuedAt(Date(getZonedDateTime().minusHours(5).toInstant().toEpochMilli()))
                    .withExpiresAt(Date(getZonedDateTime().plusHours(5).toInstant().toEpochMilli()))
                    .toSignedCookie("OutraSenhaQueAssinouOTokenGrandeOSuficiente"),
            )

        val dateTime = getZonedDateTime()

        val response = withGivenDateTime(dateTime) {
            assertThrows<HttpClientResponseException> {
                client.toBlocking().exchange(request, Argument.of(ValidateTokenResponseTO::class.java))
            }
        }

        response.status shouldBe HttpStatus.UNAUTHORIZED
    }

    companion object {
        @JvmStatic
        fun jwtUserRoles(): Stream<Arguments> = Stream.of(
            Arguments.arguments(Role.OWNER, false, true, true),
            Arguments.arguments(Role.GUEST, true, false, null),
            Arguments.arguments(Role.OWNER, false, false, false),
        )
    }
}