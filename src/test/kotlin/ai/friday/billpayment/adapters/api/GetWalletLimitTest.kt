package ai.friday.billpayment.adapters.api

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.DEFAULT_LEGACY_ACCOUNT_CONFIGURATION
import ai.friday.billpayment.adapters.dynamodb.InviteDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.InviteReminderDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.SystemActivityDbRepository
import ai.friday.billpayment.adapters.dynamodb.SystemActivityDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletLimitDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletMemberDynamoDAO
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.account.toSystemActivityKey
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT_3
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.generateCookie
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.toMember
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.withGivenDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class GetWalletLimitTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
) {
    private val enhancedClient = getDynamoDB()

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val walletDAO = WalletDynamoDAO(enhancedClient)
    private val walletMemberDAO = WalletMemberDynamoDAO(enhancedClient)
    private val inviteDAO = InviteDynamoDAO(enhancedClient)
    private val inviteReminderDAO = InviteReminderDynamoDAO(enhancedClient)
    private val walletLimitDAO = WalletLimitDynamoDAO(enhancedClient)

    private val walletRepository = WalletDbRepository(
        walletDAO = walletDAO,
        walletMemberDAO = walletMemberDAO,
        inviteDAO = inviteDAO,
        inviteReminderDAO = inviteReminderDAO,
        walletLimitDAO = walletLimitDAO,
        accountRepository = mockk(),
    )
    private val systemActivityRepository = SystemActivityDbRepository(SystemActivityDynamoDAO(enhancedClient))

    private val walletFixture = WalletFixture()
    private val primaryWallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant))

    private val founderAccountId = primaryWallet.founder.accountId
    private val primaryWalletId = primaryWallet.id

    private val anotherAccountId = AccountId(ACCOUNT_ID)

    private val collaboratorAccountId = walletFixture.participantAccount.accountId

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        loadAccountIntoDb(dynamoDB, accountId = founderAccountId, defaultWalletId = primaryWalletId)
        loadAccountIntoDb(dynamoDB, accountId = anotherAccountId)

        walletRepository.save(primaryWallet)
        walletRepository.saveLimit(
            walletId = primaryWallet.id,
            type = DailyPaymentLimitType.DAILY,
            currentAmount = 4000,
            lastAmount = 2000,
        )
    }

    @Test
    fun `should fetch wallet limit`() {
        val now = getZonedDateTime()
        withGivenDateTime(now) {
            systemActivityRepository.save(
                primaryWallet.id.toSystemActivityKey(),
                SystemActivityType.WhatsAppPayment,
                "true",
            )
            walletRepository.saveLimit(
                walletId = primaryWallet.id,
                type = DailyPaymentLimitType.NIGHTTIME,
                currentAmount = 2000,
                lastAmount = 4000,
            )
            val request = buildRequest(generateCookie(founderAccountId, Role.OWNER), primaryWalletId)
            val response = client.toBlocking().exchange(request, Argument.of(DailyPaymentLimitsTO::class.java))

            response.status() shouldBe HttpStatus.OK
            val walletLimit = response.body.get()
            walletLimit shouldBe DailyPaymentLimitsTO(
                editable = true,
                daily = DailyPaymentLimitTO(
                    activeFrom = now.plusHours(24).format(dateTimeFormat),
                    currentAmount = 2000,
                    nextAmount = 4000,
                    maxAmount = 100_000_00,
                    usedAmount = 0,
                    capped = false,
                ),
                nighttime = DailyPaymentLimitTO(
                    activeFrom = now.plusHours(24).format(dateTimeFormat),
                    currentAmount = 2000,
                    nextAmount = null,
                    maxAmount = 10_000_00,
                    usedAmount = 0,
                    capped = false,
                ),
                monthly = null,
                creditCardMonthly = CreditCardMonthlyLimitTO(currentAmount = 20_000_00, usedAmount = 0),
                whatsAppPayment = WhatsAppPaymentLimitTO(
                    activeFrom = now.plusHours(24).format(dateTimeFormat),
                    currentAmount = 50_00,
                    nextAmount = null,
                    maxAmount = 1000_00,
                ),
                automaticPix = DailyPaymentLimitTO(
                    activeFrom = now.plusHours(24).format(dateTimeFormat),
                    currentAmount = 25_000_00,
                    nextAmount = null,
                    maxAmount = 100_000_00,
                    usedAmount = 0,
                    capped = false,
                ),
            )
        }
    }

    @Test
    fun `should fetch default wallet limit`() {
        val now = getZonedDateTime()
        withGivenDateTime(now) {
            val walletFixture = WalletFixture()

            loadAccountIntoDb(dynamoDB, accountId = walletFixture.participant.accountId)

            val participantWallet = walletFixture.buildPrimaryWallet(founderAccount = walletFixture.participantAccount)
            walletRepository.save(participantWallet)

            val request =
                buildRequest(generateCookie(walletFixture.participant.accountId, Role.OWNER), participantWallet.id)
            val response = client.toBlocking().exchange(request, Argument.of(DailyPaymentLimitsTO::class.java))

            response.status() shouldBe HttpStatus.OK
            val walletLimit = response.body.get()
            walletLimit shouldBe DailyPaymentLimitsTO(
                editable = true,
                daily = DailyPaymentLimitTO(
                    activeFrom = now.plusHours(24).format(dateTimeFormat),
                    currentAmount = 25_000_00,
                    nextAmount = null,
                    maxAmount = 100_000_00,
                    usedAmount = 0,
                    capped = false,
                ),
                nighttime = DailyPaymentLimitTO(
                    activeFrom = now.plusHours(24).format(dateTimeFormat),
                    currentAmount = 1_000_00,
                    nextAmount = null,
                    maxAmount = 10_000_00,
                    usedAmount = 0,
                    capped = false,
                ),
                monthly = null,
                creditCardMonthly = CreditCardMonthlyLimitTO(currentAmount = 20_000_00, usedAmount = 0),
                whatsAppPayment = WhatsAppPaymentLimitTO(
                    activeFrom = now.plusHours(24).format(dateTimeFormat),
                    currentAmount = 50_00,
                    nextAmount = null,
                    maxAmount = 1_000_00,
                ),
                automaticPix = DailyPaymentLimitTO(
                    activeFrom = now.plusHours(24).format(dateTimeFormat),
                    currentAmount = 25_000_00,
                    nextAmount = null,
                    maxAmount = 100_000_00,
                    usedAmount = 0,
                    capped = false,
                ),
            )
        }
    }

    @Test
    fun `should return not found when wallet does not exist`() {
        val request = buildRequest(generateCookie(founderAccountId, Role.OWNER), WalletId("Inexistent-wallet-id"))
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(DailyPaymentLimitsTO::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `should return forbidden when account is not a member`() {
        val request = buildRequest(generateCookie(anotherAccountId, Role.OWNER), primaryWalletId)
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(DailyPaymentLimitsTO::class.java))
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return updated limit and default for limit not set`() {
        val request = buildRequest(generateCookie(founderAccountId, Role.OWNER), primaryWalletId)
        val response = client.toBlocking().exchange(request, Argument.of(DailyPaymentLimitsTO::class.java))

        response.status() shouldBe HttpStatus.OK
        val walletLimit = response.body.get()
        walletLimit.daily.currentAmount shouldBe 2000
        walletLimit.nighttime.currentAmount shouldBe 1_000_00L
        walletLimit.whatsAppPayment.currentAmount shouldBe 50_00L
    }

    @Test
    fun `should return updated limit and editable true when member is founder`() {
        val request = buildRequest(generateCookie(founderAccountId, Role.OWNER), primaryWalletId)
        val response = client.toBlocking().exchange(request, Argument.of(DailyPaymentLimitsTO::class.java))

        response.status() shouldBe HttpStatus.OK
        val walletLimit = response.body.get()
        walletLimit.editable shouldBe true
    }

    @Test
    fun `should return updated limit and editable false when member is not founder`() {
        loadAccountIntoDb(dynamoDB, accountId = collaboratorAccountId)
        val request = buildRequest(generateCookie(collaboratorAccountId, Role.OWNER), primaryWalletId)
        val response = client.toBlocking().exchange(request, Argument.of(DailyPaymentLimitsTO::class.java))

        response.status() shouldBe HttpStatus.OK
        val walletLimit = response.body.get()
        walletLimit.editable shouldBe false
    }

    @Test
    fun `deve retornar editable true quando a conta for simplificada desde que seja o founder`() {
        val walletId = WalletId("BASIC-USER-WALLET-ID")
        val accountId = AccountId("ACCOUNT-ID-BASIC-USER")
        val basicAccount = ACCOUNT.copy(
            accountId = accountId,
            document = DOCUMENT_3,
            name = "Fulano de Tal",
            emailAddress = EmailAddress("<EMAIL>"),
            configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION.copy(
                defaultWalletId = walletId,
            ),
        )
        val wallet = walletFixture.buildWallet(
            id = walletId,
            walletFounder = basicAccount.toMember(MemberType.FOUNDER, MemberPermissions.of(MemberType.FOUNDER)),
        )
        loadAccountIntoDb(dynamoDB, accountId = accountId, type = UserAccountType.BASIC_ACCOUNT)
        walletRepository.save(wallet)

        val request = buildRequest(generateCookie(accountId, Role.OWNER), walletId)
        val response = client.toBlocking().exchange(request, Argument.of(DailyPaymentLimitsTO::class.java))

        response.status() shouldBe HttpStatus.OK
        val walletLimit = response.body.get()
        walletLimit.editable shouldBe true
    }

    private fun buildRequest(cookie: Cookie, walletId: WalletId): MutableHttpRequest<DailyPaymentLimitsTO> {
        return HttpRequest.GET<DailyPaymentLimitsTO>("/wallet/${walletId.value}/limit")
            .cookie(cookie).header("X-API-VERSION", "2")
    }
}