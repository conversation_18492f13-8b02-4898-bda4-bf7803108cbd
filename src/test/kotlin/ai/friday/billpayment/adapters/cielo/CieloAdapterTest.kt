package ai.friday.billpayment.adapters.cielo

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.payment.PaymentNotCanceled
import ai.friday.billpayment.app.payment.PaymentNotCaptured
import ai.friday.billpayment.integration.ACCOUNT_ID
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.reactivex.Flowable
import java.io.IOException
import java.util.*
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

internal class CieloAdapterTest {

    private val mockClient: RxHttpClient = mockk()

    private val cieloConfiguration = CieloConfiguration().apply {
        authorizePath = "/1/sales/"
        capturePath = "/1/sales/{PaymentId}/capture"
        cancelPath = "/1/sales/orderid/{MerchantOrderId}/void)"
        checkStatusByMerchantOrderIdPath = "/1/sales/"
        checkStatusByPaymentIdPath = "/1/sales/{PaymentId}"
    }

    private val cieloAdapter = CieloAdapter(
        mockClient,
        cieloConfiguration,
        mutableMapOf("MerchantId" to "123", "MerchantKey" to "456"),
    )

    @Test
    @Throws(IOException::class)
    fun shouldAuthorizeCreditCardPayment() {
        every {
            mockClient.exchange(
                ofType(HttpRequest::class),
                ofType(Argument::class),
                ofType(Argument::class),
            )
        } answers { Flowable.just(HttpResponse.created(buildCieloPaymentTO())) }
        val fundsDepositOrder = cieloAdapter.authorize(
            AccountId(ACCOUNT_ID),
            SUCCESS_TRANSACTION_ID,
            15700,
            CreditCard(
                CreditCardBrand.VISA,
                "455187******0183",
                "12/2030",
                "888",
                binDetails = null,
                riskLevel = RiskLevel.LOW,
            ),
            "TESTE",
        )
        assertEquals(CreditCardPaymentStatus.AUTHORIZED, fundsDepositOrder.status)
    }

    /*@Disabled
    @Test
    fun shouldReturnDeniedOnBadRequest() {
        every {
            mockClient.exchange(
                ofType(HttpRequest::class),
                ofType(Argument::class),
                ofType(Argument::class),
            )
        } answers {
            Flowable.error<HttpResponse<String>>(
                HttpClientResponseException(
                    "erro",
                    HttpResponse.badRequest("[\\r\\n {\\r\\n \\“Code\\“: 126,\\r\\n \\“Message\\“: \\“Credit Card Expiration Date is invalid\\“\\r\\n }\\r\\n]"),
                ),
            )
        }
        val fundsDepositOrder = cieloAdapter.authorize(
            AccountId(ACCOUNT_ID),
            SUCCESS_TRANSACTION_ID,
            15700,
            CreditCard(
                CreditCardBrand.VISA,
                "455187******0183",
                "14/2030",
                "888",
                binDetails = null,
                riskLevel = RiskLevel.LOW,
            ),
            "TESTE",
        )
        assertEquals(CreditCardPaymentStatus.DENIED, fundsDepositOrder.status)
    }*/

    @Test
    @Throws(Exception::class)
    fun shouldCaptureCreditCardPayment() {
        every {
            mockClient.exchange(
                ofType(HttpRequest::class),
                Argument.of(CieloCaptureResponseTO::class.java),
                Argument.of(CieloErrorResponse::class.java),
            )
        } answers { Flowable.just(HttpResponse.created(buildCieloCaptureResponseTO(2))) }
        cieloAdapter.capture(SUCCESS_TRANSACTION_ID)
        verify {
            mockClient.exchange(
                ofType(HttpRequest::class),
                Argument.of(CieloCaptureResponseTO::class.java),
                Argument.of(CieloErrorResponse::class.java),
            )
        }
    }

    @Test
    fun shouldThrowsPaymentNotCapturedExceptionOnCaptureStatusNot2() {
        every {
            mockClient.exchange(
                ofType(HttpRequest::class),
                Argument.of(CieloCaptureResponseTO::class.java),
                Argument.of(CieloErrorResponse::class.java),
            )
        } answers { Flowable.just(HttpResponse.created(buildCieloCaptureResponseTO(10))) }
        Assertions.assertThrows(PaymentNotCaptured::class.java) { cieloAdapter.capture("2014111707") }
    }

    @Test
    fun shouldVoidCreditCardPayment() {
        every {
            mockClient.exchange(
                ofType(HttpRequest::class),
                Argument.of(CieloCancelResponseTO::class.java),
                Argument.of(CieloErrorResponse::class.java),
            )
        } answers { Flowable.just(HttpResponse.created(buildCieloCancelResponseTO(10))) }
        cieloAdapter.cancel(SUCCESS_TRANSACTION_ID)
        verify { mockClient.exchange(ofType(HttpRequest::class), ofType(Argument::class), ofType(Argument::class)) }
    }

    @Test
    fun shouldThrowsPaymentNotCanceledExceptionOnCancelStatusNot10() {
        every {
            mockClient.exchange(
                ofType(HttpRequest::class),
                Argument.of(CieloCancelResponseTO::class.java),
                Argument.of(CieloErrorResponse::class.java),
            )
        } answers { Flowable.just(HttpResponse.created(buildCieloCancelResponseTO(2))) }
        Assertions.assertThrows(PaymentNotCanceled::class.java) { cieloAdapter.cancel("2014111709") }
    }

    companion object {
        private const val SUCCESS_TRANSACTION_ID = "2014111706"
    }

    private fun buildCieloCaptureResponseTO(status: Int): CieloCaptureResponseTO = CieloCaptureResponseTO().apply {
        authorizationCode = UUID.randomUUID().toString()
        this.status = status
        returnCode = "000"
    }

    private fun buildCieloCancelResponseTO(status: Int): CieloCancelResponseTO = CieloCancelResponseTO().apply {
        this.status = status
        authorizationCode = UUID.randomUUID().toString()
        returnCode = "000"
    }

    @Throws(IOException::class)
    fun buildCieloPaymentTO(): CieloPaymentResponseTO? {
        val stream = Thread.currentThread().contextClassLoader.getResourceAsStream("cielo-payment-response.json")
        return jacksonObjectMapper().readerFor(CieloPaymentResponseTO::class.java)
            .readValue<CieloPaymentResponseTO>(stream)
    }
}