/*
package ai.friday.billpayment.adapters.btg

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.itp.ITPService
import io.micronaut.http.HttpRequest
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
@MicronautTest(environments = [FRIDAY_ENV])
internal class BtgCallbackIntegrationLocalTest(embeddedServer: EmbeddedServer) {

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(ITPService::class)
    fun getITPService() = itpService
    private val itpService: ITPService = mockk()

    @Test
    fun `efetua uma chamada`() {
        val map: Map<String, String> = mapOf("dado1" to "valor 1")
        val request = HttpRequest.POST("/btg/webhooks", map)
            .header("x-custom-header", "2")
        client.toBlocking().exchange(request, String::class.java)
    }
}*/