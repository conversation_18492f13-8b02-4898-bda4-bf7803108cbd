/*
package ai.friday.billpayment.adapters.btg

import ai.friday.billpayment.adapters.itp.ITPConsentCallbackTO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.itp.PaymentIntent
import ai.friday.billpayment.app.itp.PaymentIntentDetails
import ai.friday.billpayment.app.itp.PaymentIntentStatus
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.WALLET_ID
import arrow.core.getOrElse
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.collections.shouldNotContain
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import java.net.URL
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
@MicronautTest(environments = [FRIDAY_ENV])
internal class BtgCallbackIntegrationTest(embeddedServer: EmbeddedServer, private val btgITPAdapter: BtgITPAdapter) {

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @Test
    fun `criar iniciação de pagamento com chave pix`() {
        val result = btgITPAdapter.createPaymentRedirectUrl(
            PaymentIntent(
                accountId = AccountId(ACCOUNT_ID),
                walletId = WalletId(WALLET_ID),
                document = "***********",
                authorizationServerId = "********-ec0d-4398-83ce-68b6b1087e49",
                authorizationServerName = "Xpto",
                routingNo = 1,
                accountNo = 2,
                accountDv = "",
                bankISPB = "********",
                accountType = null,
                bankNo = null,
                forecastPeriod = ForecastPeriod.TODAY,
                includeScheduledBillsOnly = false,
                details = PaymentIntentDetails.WithPixKey(pixKey = PixKey(value = "$<EMAIL>", type = PixKeyType.EMAIL), amount = 1),
                status = PaymentIntentStatus.CREATED,
            ),
        )

        result.isRight() shouldBe true
    }

    @Test
    fun `criar iniciação de pagamento com qr code`() {
        val result = btgITPAdapter.createPaymentRedirectUrl(
            PaymentIntent(
                accountId = AccountId(ACCOUNT_ID),
                walletId = WalletId(WALLET_ID),
                document = "***********",
                authorizationServerId = "********-ec0d-4398-83ce-68b6b1087e49",
                authorizationServerName = "Xpto",
                routingNo = 1,
                accountNo = 2,
                accountDv = "",
                bankISPB = "********",
                accountType = null,
                bankNo = null,
                forecastPeriod = ForecastPeriod.TODAY,
                includeScheduledBillsOnly = false,
                details = PaymentIntentDetails.WithQRCode("00020126330014BR.GOV.BCB.PIX01110322627001052040000530398654040.015802BR5925Joao Pedro da Costa Breta6009SAO PAULO61080540900062240520e3JfvLFl8OwUdXk2iyj2630481A2"),
                status = PaymentIntentStatus.CREATED,
            ),
        )

        result.isRight() shouldBe true
    }

    @Test
    fun `testa listagem de bancos habilitados`() {
        val listOrganizations =
            btgITPAdapter.listFinancialInstitutions().getOrElse { throw IllegalStateException(it.message) }
        listOrganizations.shouldNotBeEmpty()
        listOrganizations.sortedBy {
            it.authorizationServerId
        }.forEach {
            println("${it.friendlyName} - ${it.authorizationServerId} - ${it.ispb}")
        }
    }

    @Test
    fun `testa com um banco`() {
        val listOrganizations = btgITPAdapter.listFinancialInstitutions().getOrElse { TODO() }

        val organization = listOrganizations.single {
            it.authorizationServerId == "756b9782-d9d4-4f9b-9756-997eba0e2cbd"
        }

        if (organization.ispb != null) {
            val paymentIntent = PaymentIntent(
                accountId = AccountId(ACCOUNT_ID),
                walletId = WalletId(WALLET_ID),
                document = "***********",
                authorizationServerId = organization.authorizationServerId,
                authorizationServerName = organization.friendlyName,
                routingNo = 0,
                accountNo = 0,
                accountDv = "",
                bankISPB = organization.ispb,
                accountType = AccountType.CHECKING,
                bankNo = null,
                forecastPeriod = ForecastPeriod.TODAY,
                includeScheduledBillsOnly = false,
                details = PaymentIntentDetails.WithPixKey(pixKey = PixKey(value = "$<EMAIL>", type = PixKeyType.EMAIL), amount = 1),
                status = PaymentIntentStatus.CREATED,
            )

            val callbackUrl = btgITPAdapter.createPaymentRedirectUrl(paymentIntent)
            println(callbackUrl)
        }
    }

    @Test
    fun `testa criacao de payment intent para todos os bancos habilitados`() {
        val listOrganizations = btgITPAdapter.listFinancialInstitutions().getOrElse { TODO() }

        listOrganizations.map { organization ->
            if (organization.ispb != null) {
                val paymentIntent = PaymentIntent(
                    accountId = AccountId(ACCOUNT_ID),
                    walletId = WalletId(WALLET_ID),
                    document = "***********",
                    authorizationServerId = organization.authorizationServerId,
                    authorizationServerName = organization.friendlyName,
                    routingNo = 0,
                    accountNo = 0,
                    accountDv = "",
                    bankISPB = organization.ispb,
                    accountType = AccountType.CHECKING,
                    bankNo = null,
                    forecastPeriod = ForecastPeriod.TODAY,
                    includeScheduledBillsOnly = false,
                    details = PaymentIntentDetails.WithPixKey(pixKey = PixKey(value = "$<EMAIL>", type = PixKeyType.EMAIL), amount = 1),
                    status = PaymentIntentStatus.CREATED,
                )

                btgITPAdapter.createPaymentRedirectUrl(paymentIntent).map { _ ->
                    println("${organization.authorizationServerId};${organization.friendlyName};OK")
                }.getOrElse {
                    println("${organization.authorizationServerId};${organization.friendlyName};FAIL")
                }
            }
        }
    }

    @Test
    fun `testa a integração com o CRUD de cadastro de URLs`() {
        val urlToBeCreated = "https://use.meupagador.com.br/web/saldo-adicionado-itp"
        val description = "Rota ambiente STG"
        val result = btgITPAdapter.createPaymentRedirectURL(urlToBeCreated, description)
        result.isRight() shouldBe true

        val listAllResult = btgITPAdapter.listPaymentRedirectURLs()
        listAllResult.isRight() shouldBe true
        listAllResult.map { listAll ->
            listAll shouldContain Pair(urlToBeCreated, description)
        }

        Thread.sleep(3000)

        val deleteResult = btgITPAdapter.deletePaymentRedirectURL(urlToBeCreated)
        deleteResult.isRight() shouldBe true

        val listAllResultAfterDeletion = btgITPAdapter.listPaymentRedirectURLs()
        listAllResultAfterDeletion.isRight() shouldBe true
        listAllResultAfterDeletion.map { listAll ->
            listAll shouldNotContain Pair(urlToBeCreated, description)
        }
    }

    @Test
    fun `cria pagamento com o mockBanking`() {
        val urlToRedirect = btgITPAdapter.createPaymentRedirectUrl(
            PaymentIntent(
                accountId = AccountId(ACCOUNT_ID),
                walletId = WalletId(WALLET_ID),
                document = "***********",
                authorizationServerId = "b6bd522b-5fe2-427c-880e-6bb8e9c71672",
                authorizationServerName = "Xpto",
                routingNo = 1,
                accountNo = 2,
                accountDv = "",
                bankISPB = "********",
                accountType = null,
                bankNo = null,
                forecastPeriod = ForecastPeriod.TODAY,
                includeScheduledBillsOnly = false,
                details = PaymentIntentDetails.WithPixKey(pixKey = PixKey(value = "$<EMAIL>", type = PixKeyType.EMAIL), amount = 1234),
                status = PaymentIntentStatus.CREATED,
            ),
        ).getOrElse { throw IllegalStateException(it.message) }
        println(urlToRedirect)
    }

    @Test
    fun `testa callback de consentimento`() {
        val receivedURL = "https://use.friday.ai/web/testeDeIntegracao#error=access_denied&error_description=Consentimento%20rejeitado%20pelo%20usu%C3%A1rio&state=B1bulDFT3SOEEFvpnKka686V83zONdEptrIGYkxvM6M"
        // "https://use.friday.ai/web/testeDeIntegracao#code=flpvANUt_NRu6zdhvl3q6TYG8mjjxUyaxRWIqgqgAx9&id_token=eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZHQ00iLCJjdHkiOiJKV1QiLCJraWQiOiJlYTIwN2RjYTRjMDE3MGIxODY4ZGYxM2VmNmM1NmJjMzk0NjJmZDczOWRkYzFkMWNiMmQyYzdiMDM0YzExODA1In0.G5HWVDod9vJRr8GA2ykARclUSyjNrSfljaP7Po4N3g6nOxH-mWtHhHM6qe2JjvJxVotlkeQu2s2orh2jTDZwudjb1E-PAYtoB-iJrTUcnY1XpWFoL9JGRbN7Zh2yDCfP55hYvCImgllznSYwUJMJDKpriwiCwGGR3I6P0XKkICi3upx3GzXxrgn_ChcLlUutIKy4oucfc6j6Xj8UMMWVhUYt9nftJ1oJ_1Su229R4yfCTZQcTwGn0fdk9uI9dxAwbhpTmcwmIayN9eTvLQzN8v82BDyOH-CjOHhccL-oaVSQWKyt8CvwSCoBMY52DcQRAU40Swz4HFGM5MmZVYi2vw.WV-N4r84EbbbgTtf.GT8aseEhgAN0mGEXayD3yd9s7wlHpyPSw7SOqMOtW1Uzm6bceQ6ISpPNLr7JqrNCb-hWsiq8MV8arp0PAWGWutC0TJNuGecdPK48V5KnR6bgVbmSOLL5j-ucZaGKVvhS48YciQ8nLIs60G4vaSSbuduPoORZ_21xYmANqAeR6dxTBQt2v3Vk-78zffIoz9RcTZo_OyNwPs30Dzg-rYWagDNWF_J1RS9aKsiM_d0wQyLA2YkP_sXTTEf3VfG7-SsYXS8WoHh9fbOnvhCeAmt43pLRC9cLVeNndQyIRsIwlDGEGqbEh4DLBsqGkx_IEcOajQJyF520gtGl4pPy0wXSVqcbaEhaB9d3GxTb9KrYjo58hQ9cZcLj-7uddcMJm1CZv_sfUq7BWbQ5lbCH2SgrG5If6dQy-HeNa1tjMaAmKNG2rAcVEPiwLL3Sy5mpm4DKSneBOeowGgvZ34XNoYVlZdAwWBZX8FOFFcf_9JSvViRZwAZ05AkZ-_q03NMKh2fCXuLbTbhuXPECli9Cxn27bN-CdAS1aABNyM4ExRUHx-YjOhbrpUxJCSm_cfBlF0a4jlGKPyXv8B29YOYa84bdc7bhHleco3KsGGdoj1-VBdU-dnIWXjo-_uLs5J74qYbmWwgz6mQGQDI6tpsDuF0ayLvkU1g8Pqw7_R4CdUXyaTwS1aLqHKlv1lahJB2S7DSDxswNNo-sfaAo2m0YppNc9Ac5ZIFE4QeSH_K7roa8Owid7Moh9Sz5lz9CQoqnqLHQMYptko38cSuoZ39Dqtod6gO0dr3vdUjn4DxGhq_Q48hAwKCRxJUgDRQZsj-UFuwLfqFBw9UX4ABI0NmZN5mlb8FV9F9JjgkeRyzrQjQRQRXsdlaBpIxj8QDtHbl0m3qP93wDAHty_L4YqKpSfDCe3vKYOJ-T9Z8a3eGitTeaQJGGHFTeWizUrgwU7ZnxlvJ0iLD5J7UPewlExn3z_HLyHoN17EHyyliF89q-tg2XY_j2-5viqr3RWAwCjdUhXP4Z3M5UmHiyQpwun368mBJWlJK36UjyuFcYZIC-QGyzLacDJYKuajbhW4mVjbv7k1zousq265dn2rYkk2Y5DBfGrDOpsDOHnAhe8HcD8RkQPHYNurhKH-HbFfPGXWpqp6o5sNnBNH5_-Nxz.uaqwA5dRnELQj38qI9Famg&state=pG8An-4dgpnbFf__A3MTssXoAMyU2e7kotDwWeMPy8E"

        val consentCallbackPayload = parseCallbackURL(receivedURL)

        val request = HttpRequest.POST("/itp/payment/callback", consentCallbackPayload)

        val result = client.toBlocking().exchange(request, Argument.of(Unit::class.java))

        result.status shouldBe HttpStatus.NO_CONTENT
    }

    private fun parseCallbackURL(url: String): ITPConsentCallbackTO {
        val query = URL(url).query
        val splitted = if (!query.isNullOrBlank()) {
            query.split("&")
        } else {
            url.split("#")[1].split("&")
        }
        val values = splitted.associate {
            val (key, value) = it.split("=")
            key to value
        }

        return ITPConsentCallbackTO(
            state = values["state"]!!,
            code = values["code"],
            idToken = values["id_token"],
            error = values["error"],
            errorDescription = values["error_description"],
        )
    }
}*/