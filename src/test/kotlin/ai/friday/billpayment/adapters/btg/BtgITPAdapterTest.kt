/*
package ai.friday.billpayment.adapters.btg

import ai.friday.billpayment.aJsonErrorResponse
import ai.friday.billpayment.aJsonResponse
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.chatbot.Organization
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.itp.ITPAdapter
import ai.friday.billpayment.app.itp.ITPServiceError
import ai.friday.billpayment.app.itp.PaymentIntent
import ai.friday.billpayment.app.itp.PaymentIntentDetails
import ai.friday.billpayment.app.itp.PaymentIntentStatus
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.assertHeaders
import ai.friday.billpayment.assertJsonBody
import ai.friday.billpayment.externalBankAccount
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.mockServer
import arrow.core.getOrElse
import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.client.WireMock.get
import com.github.tomakehurst.wiremock.client.WireMock.getRequestedFor
import com.github.tomakehurst.wiremock.client.WireMock.post
import com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor
import com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.http.MediaType
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.net.URL
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail

@Disabled
internal class BtgITPAdapterTest {
    private lateinit var adapter: ITPAdapter

    private lateinit var wm: WireMockServer

    private val validToken = """{"expire_in":1200,"access_token":"xpto"}"""
    private val validInstitutionResponse =
        """
        {"version":"1","status":200,"body":[{"organizationId":"organization_id","authorizationServerId":"authorization_service_id","status":"ACTIVE","customerFriendlyName":"customer_friendly_name","customerFriendlyDescription":"customer_friendly_description","customerFriendlyLogoUri":"customer_friendly_logo_uri","organizationName":"organization_name","legalEntityName":"legal_entity_name","ispb":"ispb","clearingCode":"clearing_code","cnpj":"cnpj","apiFamilyTypes":["api_family_types"]}]}
        """.trimIndent()
    private val validCreatePaymentResponse =
        """
        {"version":"version","status":200,"body":{"pactualId":"pactual_id","pactualBulkId":"pactual_bulk_id","clientRequestId":"client_request_id","createTimestamp":"create_timestamp","lastUpdateTimestamp":"last_update_timestamp","entity":"entity","status":"status","body":{"consentRedirectUrl":"consent_redirect_url"},"tags":[]}}
        """.trimIndent()

    private val paymentIntent =
        PaymentIntent(
            accountId = AccountId(ACCOUNT_ID),
            walletId = WalletId(WALLET_ID),
            document = externalBankAccount.document.value,
            authorizationServerId = "authorization_server_id",
            authorizationServerName = "title",
            routingNo = externalBankAccount.routingNo,
            accountNo = externalBankAccount.accountNo,
            accountDv = externalBankAccount.accountDv,
            bankISPB = "********",
            accountType = AccountType.CHECKING,
            bankNo = externalBankAccount.bankNo,
            forecastPeriod = ForecastPeriod.FIFTEEN_DAYS,
            includeScheduledBillsOnly = false,
            details = PaymentIntentDetails.WithPixKey(pixKey = PixKey(value = "$<EMAIL>", type = PixKeyType.EMAIL), amount = 1),
            status = PaymentIntentStatus.CREATED,
        )

    @BeforeEach
    internal fun setUp() {
        wm = mockServer()

        adapter =
            BtgITPAdapter(
                RxHttpClient.create(URL(wm.baseUrl())),
                BtgITPConfiguration().apply {
                    clientId = "client_id"
                    clientSecret = "client_secret"
                    accessTokenPath = "/token"
                    createPaymentPath = "/payment"
                    listOrganizationPath = "/list"
                    redirectUrl = "redirect_url"
                    grantType = "grant_type"
                },
            )
    }

    @Test
    internal fun `should handle token request error`() {
        wm.stubFor(post("/token").willReturn(aJsonErrorResponse()))

        adapter.listFinancialInstitutions().getOrElse {
            Assertions.assertTrue(it is ITPServiceError.ITPErrorWithException.ITPProviderError)
            return
        }

        fail("should return left with error")
    }

    @Test
    fun `should list institutions request`() {
        wm.stubFor(post("/token").willReturn(aJsonResponse(validToken)))
        wm.stubFor(get("/list").willReturn(aJsonResponse(validInstitutionResponse)))

        val result = adapter.listFinancialInstitutions().getOrElse { fail(it.toString()) }

        with(result) {
            this.shouldNotBeNull()
            this.size shouldBe 1
            this shouldBe listOf(Organization("authorization_service_id", null, "ispb", "customer_friendly_name"))
        }

        wm.verify(getRequestedFor(urlEqualTo("/list")).assertHeaders("Authorization" to "Bearer xpto"))
    }

    @Test
    fun `should handle list institutions request error`() {
        wm.stubFor(post("/token").willReturn(aJsonResponse(validToken)))
        wm.stubFor(get("/list").willReturn(aJsonErrorResponse()))

        adapter.listFinancialInstitutions().getOrElse {
            Assertions.assertTrue(it is ITPServiceError.ITPErrorWithException.ITPProviderError)
            return
        }

        fail("should return left with error")
    }

    @Test
    fun `should handle create payment request`() {
        wm.stubFor(post("/token").willReturn(aJsonResponse(validToken)))
        wm.stubFor(post("/payment").willReturn(aJsonResponse(validCreatePaymentResponse)))

        val paymentRequest =
            adapter.createPaymentRedirectUrl(
                paymentIntent,
            )

        val result = paymentRequest.getOrElse { fail(it.toString()) }

        result shouldBe "consent_redirect_url"

        wm.verify(
            postRequestedFor(urlEqualTo("/payment"))
                .assertHeaders("Authorization" to "Bearer xpto", "Content-Type" to MediaType.APPLICATION_JSON)
                .assertJsonBody(
                    "amount" to 1,
                    "pixKey" to "<EMAIL>",
                    "authorizationServerId" to "authorization_server_id",
                    "loggedUser.identification" to "08845856704",
                    "loggedUser.type" to "CPF",
                ),
        )
    }

    @Test
    fun `should handle create payment request error`() {
        wm.stubFor(post("/token").willReturn(aJsonResponse(validToken)))
        wm.stubFor(post("/payment").willReturn(aJsonErrorResponse()))

        val paymentRequest =
            adapter.createPaymentRedirectUrl(
                paymentIntent,
            )

        paymentRequest.getOrElse {
            Assertions.assertTrue(it is ITPServiceError.ITPErrorWithException.ITPProviderError)
            return
        }

        fail("should return left with error")
    }
}*/