package ai.friday.billpayment.adapters.btg

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.itp.ConsentId
import ai.friday.billpayment.app.itp.ITPService
import ai.friday.billpayment.app.itp.PaymentIntent
import ai.friday.billpayment.app.itp.PaymentIntentDetails
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.itp.PaymentIntentStatus
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.WALLET_ID
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class BTGCallbackControllerTest {

    private val itpService = mockk<ITPService>() {
        every {
            listAllStaticInstitutions()
        } returns emptyMap()
    }
    private val btgCallbackController = BTGCallbackController(itpService = itpService)

    private val paymentIntentId = PaymentIntentId("PAYMENT-INTENT-9dfafcba-95ec-4433-83f1-f4e88fa55799")

    private val defaultTO = BTGTO(
        body = BTGBodyTO(
            amount = 123,
            authorizationServerId = "authorizationServerId",
            consent = "{}",
            consentRedirectUrl = "consentRedirectUrl",
            creditorAccount = "{}",
        ),
        clientRequestId = "${paymentIntentId.value}#*************",
        createTimestamp = "2022-10-17T21:30:25.2679044+00:00",
        entity = "PaymentInitiationPix",
        pactualId = "3f3ab92c-be4f-4674-b833-b7a72b118797",
        status = "AWAITING_AUTHORISATION",
    )

    private val defaultPaymentIntent = PaymentIntent(
        accountId = AccountId(ACCOUNT_ID),
        walletId = WalletId(WALLET_ID),
        document = DOCUMENT,
        authorizationServerId = "AUTHORIZATION_ID",
        authorizationServerName = "AUTHORIZATION_NAME",
        routingNo = 123,
        accountNo = 321,
        accountDv = "",
        bankISPB = "1234",
        accountType = AccountType.CHECKING,
        bankNo = null,
        forecastPeriod = ForecastPeriod.SEVEN_DAYS,
        includeScheduledBillsOnly = false,
        details = PaymentIntentDetails.WithPixKey(pixKey = PixKey(value = "$<EMAIL>", type = PixKeyType.EMAIL), amount = 0),
        status = PaymentIntentStatus.CREATED,
    )

    private val request = mockk<HttpRequest<*>>() {
        every { headers } returns mockk() {
            every {
                asMap()
            } returns emptyMap()
        }
    }

    @ParameterizedTest
    @MethodSource("createBTGStatusAndDesiredPaymentIntentStatus")
    fun `deve chamar o itpService quando for uma notificação de PaymentInitiationPix`(
        BTGStatus: String,
        paymentIntentStatus: PaymentIntentStatus,
    ) {
        every {
            itpService.processPaymentIntentStatusChanged(any<ConsentId>(), any())
        } returns defaultPaymentIntent.right()

        val response =
            btgCallbackController.receiveCallback(getObjectMapper().writeValueAsString(defaultTO.copy(status = BTGStatus)), request)

        verify {
            itpService.processPaymentIntentStatusChanged(paymentIntentId, paymentIntentStatus)
        }

        response.status shouldBe HttpStatus.OK
    }

    @Test
    fun `deve retornar sucesso mesmo que a chamada para o itpservice falhe`() {
        every {
            itpService.processPaymentIntentStatusChanged(any<ConsentId>(), any())
        } returns NoStackTraceException("Fake").left()

        val response = btgCallbackController.receiveCallback(getObjectMapper().writeValueAsString(defaultTO), request)

        verify {
            itpService.processPaymentIntentStatusChanged(paymentIntentId, PaymentIntentStatus.ONGOING)
        }

        response.status shouldBe HttpStatus.OK
    }

    @Test
    fun `deve retornar sucesso e não chamar o itpservice se a mensagem não for de atualização de status`() {
        every {
            itpService.processPaymentIntentStatusChanged(any<ConsentId>(), any())
        } returns NoStackTraceException("Fake").left()

        val response =
            btgCallbackController.receiveCallback(getObjectMapper().writeValueAsString(defaultTO.copy(entity = "other")), request)

        verify {
            itpService wasNot called
        }

        response.status shouldBe HttpStatus.OK
    }

    companion object {
        @JvmStatic
        fun createBTGStatusAndDesiredPaymentIntentStatus(): Stream<Arguments> {
            return Stream.of(
                Arguments.of("CONSENT_REJECTED", PaymentIntentStatus.FAILED),
                Arguments.of("PAYMENT_REJECTED", PaymentIntentStatus.FAILED),
                Arguments.of("ERROR", PaymentIntentStatus.FAILED),

                Arguments.of("SETTLEMENT_COMPLETED", PaymentIntentStatus.SUCCESS),

                Arguments.of("ENQUEUED", PaymentIntentStatus.ONGOING),
                Arguments.of("AWAITING_AUTORIZATION", PaymentIntentStatus.ONGOING),
                Arguments.of("AUTHORIZED", PaymentIntentStatus.ONGOING),
                Arguments.of("PAYMENT_PENDING", PaymentIntentStatus.ONGOING),
                Arguments.of("PARTIALY_ACCEPTED", PaymentIntentStatus.ONGOING),
                Arguments.of("SETTLEMENT_IN_PROGRESS", PaymentIntentStatus.ONGOING),
                Arguments.of("SETTLEMENT_DEBITOR_ACCOUNT", PaymentIntentStatus.ONGOING),
                Arguments.of("ANY_OTHER_STATUS", PaymentIntentStatus.ONGOING),
            )
        }
    }
}

private data class BTGBodyTO(
    val amount: Int,
    val authorizationServerId: String,
    val consent: String,
    val consentRedirectUrl: String,
    val creditorAccount: String,
)

private data class BTGTO(
    private val body: BTGBodyTO,
    val clientRequestId: String,
    val createTimestamp: String,
    val entity: String,
    val pactualId: String,
    val status: String,
)