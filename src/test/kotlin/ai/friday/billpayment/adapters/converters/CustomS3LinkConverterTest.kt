package ai.friday.billpayment.adapters.converters

import ai.friday.billpayment.app.account.CustomS3Link
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class CustomS3LinkConverterTest {

    private val converter = CustomS3LinkConverter()

    @Test
    fun `should convert CustomS3Link to JSON string`() {
        // Given
        val customS3Link = CustomS3Link(
            bucket = "test-bucket",
            key = "test-key",
            region = "us-east-1",
        )

        // When
        val result = converter.convert(customS3Link)

        // Then
        assertTrue(result.contains("\"bucket\":\"test-bucket\""))
        assertTrue(result.contains("\"key\":\"test-key\""))
        assertTrue(result.contains("\"region\":\"us-east-1\""))
        assertTrue(result.contains("\"s3\":"))
    }

    @Test
    fun `should unconvert JSON string to CustomS3Link`() {
        // Given
        val json = """{"s3":{"bucket":"test-bucket","key":"test-key","region":"us-east-1"}}"""

        // When
        val result = converter.unconvert(json)

        // Then
        assertEquals("test-bucket", result.bucket)
        assertEquals("test-key", result.key)
        assertEquals("us-east-1", result.region)
    }

    @Test
    fun `should handle round trip conversion`() {
        // Given
        val original = CustomS3Link(
            bucket = "via1-user-documents",
            key = "user_documents/ACCOUNT-49099ed8-c5df-41d0-8de6-54688da369ec/CONTRACT_1658256500.pdf",
            region = "us-west-2",
        )

        // When
        val json = converter.convert(original)
        val restored = converter.unconvert(json)

        // Then
        assertEquals(original.bucket, restored.bucket)
        assertEquals(original.key, restored.key)
        assertEquals(original.region, restored.region)
    }
}