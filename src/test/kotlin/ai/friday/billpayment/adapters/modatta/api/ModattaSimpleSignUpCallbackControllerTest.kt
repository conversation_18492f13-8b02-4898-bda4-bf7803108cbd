/*
package ai.friday.billpayment.adapters.modatta.api

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.modatta.register.ConflictReason
import ai.friday.billpayment.app.modatta.register.ModattaSimpleSignUpService
import ai.friday.billpayment.app.modatta.register.ModattaSimpleSignUpUpdateStatusRequest
import ai.friday.billpayment.app.modatta.register.SimpleSignUpApprover
import ai.friday.billpayment.app.modatta.register.SimpleSignUpCallbackError
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

@Disabled
@MicronautTest(environments = [MODATTA_ENV, "stgmodatta"])
class ModattaSimpleSignUpCallbackControllerTest(
    embeddedServer: EmbeddedServer,
) {
    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @field:Property(name = "friday-callback.identity")
    lateinit var fridayIdentity: String

    @field:Property(name = "friday-callback.secret")
    lateinit var fridaySecret: String

    @MockBean(ModattaSimpleSignUpService::class)
    fun modattaSimpleSignUpService() = modattaSimpleSignUpService
    private val modattaSimpleSignUpService: ModattaSimpleSignUpService = mockk()

    private val updateStatusRequest = ModattaSimpleSignUpUpdateStatusRequestTO(
        accountId = "friday-account-id",
        status = AccountStatus.APPROVED,
        approvedBy = SimpleSignUpApprover.EXTERNAL_ACCOUNT_REGISTER,
        externalId = "ACCOUNT-id-da-modatta",
        externalIdProviderName = AccountProviderName.MODATTA,
    )

    @Test
    fun `deve retornar bad request ao receber um provider name invalido`() {
        every {
            modattaSimpleSignUpService.updateStatus(any())
        } answers {
            val request: ModattaSimpleSignUpUpdateStatusRequest = firstArg()
            SimpleSignUpCallbackError.InvalidProviderName(providerName = request.externalId.providerName).left()
        }

        val request = HttpRequest.POST("/callback/simpleSignUp/updateStatus", updateStatusRequest)
            .basicAuth(fridayIdentity, fridaySecret)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST

        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            code shouldBe "4001"
            message shouldBe "Invalid provider name: ${updateStatusRequest.externalIdProviderName}"
        }
    }

    @Test
    fun `deve retornar not found ao receber um account id inexistente`() {
        every {
            modattaSimpleSignUpService.updateStatus(any())
        } answers {
            val request: ModattaSimpleSignUpUpdateStatusRequest = firstArg()
            SimpleSignUpCallbackError.AccountNotFound(accountId = request.fridayAccountId).left()
        }

        val request = HttpRequest.POST("/callback/simpleSignUp/updateStatus", updateStatusRequest)
            .basicAuth(fridayIdentity, fridaySecret)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND

        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            code shouldBe "4041"
            message shouldBe "Account not found: ${updateStatusRequest.accountId}"
        }
    }

    @Test
    fun `deve retornar server error quando nao conseguir tratar a requisicao`() {
        every {
            modattaSimpleSignUpService.updateStatus(any())
        } throws NoStackTraceException("some message")

        val request = HttpRequest.POST("/callback/simpleSignUp/updateStatus", updateStatusRequest)
            .basicAuth(fridayIdentity, fridaySecret)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR

        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            code shouldBe "5001"
            message shouldBe "some message"
        }
    }

    @ParameterizedTest
    @EnumSource(ConflictReason::class)
    fun `deve retornar conflito ao receber uma requisicao inconsistente com o estado atual da conta`(conflictReason: ConflictReason) {
        every {
            modattaSimpleSignUpService.updateStatus(any())
        } returns SimpleSignUpCallbackError.Conflict(reason = conflictReason).left()

        val request = HttpRequest.POST("/callback/simpleSignUp/updateStatus", updateStatusRequest)
            .basicAuth(fridayIdentity, fridaySecret)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.CONFLICT

        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            code shouldBe "4091"
            message shouldBe "Reason: ${conflictReason.name}"
        }
    }

    @Test
    fun `deve retornar accepted ao receber uma requisicao valida`() {
        val slot = slot<ModattaSimpleSignUpUpdateStatusRequest>()
        every {
            modattaSimpleSignUpService.updateStatus(capture(slot))
        } answers {
            ACCOUNT.right()
        }

        val request = HttpRequest.POST("/callback/simpleSignUp/updateStatus", updateStatusRequest)
            .basicAuth(fridayIdentity, fridaySecret)

        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.ACCEPTED

        with(slot.captured) {
            this.fridayAccountId.value shouldBe updateStatusRequest.accountId
            this.externalId.value shouldBe updateStatusRequest.externalId
            this.externalId.providerName shouldBe updateStatusRequest.externalIdProviderName
            this.status shouldBe updateStatusRequest.status
            this.approvedBy shouldBe updateStatusRequest.approvedBy
        }
    }
}*/