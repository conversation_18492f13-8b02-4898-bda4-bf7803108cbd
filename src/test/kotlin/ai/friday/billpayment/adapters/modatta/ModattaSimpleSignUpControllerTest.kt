/*
package ai.friday.billpayment.adapters.modatta

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.modatta.api.ModattaSimpleSignUpStatusTO
import ai.friday.billpayment.adapters.modatta.api.ModattaSimpleSignUpTO
import ai.friday.billpayment.adapters.modatta.api.UserDataTO
import ai.friday.billpayment.adapters.modatta.api.toUserData
import ai.friday.billpayment.adapters.modatta.dynamodb.ModattaSimpleSignUpDbRepository
import ai.friday.billpayment.adapters.modatta.dynamodb.ModattaSimpleSignUpDynamoDAO
import ai.friday.billpayment.adapters.s3.S3LinkGenerator
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.AgreementFilesService
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.UserFilesConfiguration
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.integrations.ModattaBassConfiguration
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessSelfieError
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.modatta.UserId
import ai.friday.billpayment.app.modatta.integrations.PartnerSimpleSignUpService
import ai.friday.billpayment.app.modatta.register.Enrollment
import ai.friday.billpayment.app.modatta.register.ModattaSimpleSignUp
import ai.friday.billpayment.app.modatta.register.ModattaSimpleSignUpService
import ai.friday.billpayment.app.modatta.register.ModattaSimpleSignUpValidationStatus
import ai.friday.billpayment.app.modatta.register.PartnerSimpleSignUpRequest
import ai.friday.billpayment.app.modatta.register.PartnerSimpleSignUpResult
import ai.friday.billpayment.app.modatta.register.PartnerSimpleSignUpValidation
import ai.friday.billpayment.app.modatta.register.SimpleSignUpStatus
import ai.friday.billpayment.app.modatta.register.UserContract
import ai.friday.billpayment.app.modatta.register.UserData
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.externalAccountPayment
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.buildModattaCookie
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.withGivenDateTime
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.PropertySource
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import io.via1.communicationcentre.app.ContractForm
import io.via1.communicationcentre.app.ContractSignature
import io.via1.communicationcentre.app.integrations.ContractWriterService
import java.io.InputStream
import java.io.OutputStream
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient

const val agreementFileUrl = "https://s3.file/termos_de_uso.pdf"

@Disabled
@MicronautTest(environments = [MODATTA_ENV, "stgmodatta"])
@PropertySource(
    value = [
        Property(name = "urls.termsOfUse", value = agreementFileUrl),
        Property(name = "baas.allowedIps", value = "************"),
    ],
)
class ModattaSimpleSignUpControllerTest(
    embeddedServer: EmbeddedServer,
    private val dynamoDB: AmazonDynamoDB,
    private val modattaSimpleSignUpService: ModattaSimpleSignUpService,
    private val walletService: WalletService,
    private val userFilesConfiguration: UserFilesConfiguration,
) {
    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @field:Property(name = "modatta-corp-callback.identity")
    lateinit var modattaCallbackIdentity: String

    @field:Property(name = "modatta-corp-callback.secret")
    lateinit var modattaCallbackSecret: String

    @MockBean(LivenessService::class)
    fun livenessService() = livenessService

    private val livenessService =
        mockk<LivenessService> {
            every {
                retrieveEnrollmentSelfie(any())
            } returns LivenessSelfieError.Unavailable.left()
        }

    @MockBean(PartnerSimpleSignUpService::class)
    fun partnerSimpleSignUpService() = partnerSimpleSignUpService

    private val partnerSimpleSignUpService = mockk<PartnerSimpleSignUpService>()

    @MockBean(AccountService::class)
    fun accountService() = accountService

    private val accountService = mockk<AccountService>()

    private val userContractFileUrl = "https://api.via1.app/test/register/userContract"

    @MockBean(S3LinkGenerator::class)
    fun getS3LinkGenerator() = s3LinkGenerator

    private val s3LinkGenerator: S3LinkGenerator =
        mockk {
            every { generate(any(), any(), any()) } returns userContractFileUrl
        }

    private val clientIP = "************"

    @MockBean(ModattaBassConfiguration::class)
    fun getBaasConfiguration() = baasConfiguration

    private val baasConfiguration: ModattaBassConfiguration =
        mockk {
            every { allowedIps } returns listOf(clientIP)
        }

    private val contractWriterService: ContractWriterService =
        mockk {
            every {
                createContract(any(), any(), any())
            } answers {
                val os: OutputStream = firstArg()

                os.write("mocked-byte-array".toByteArray())
            }
        }

    @MockBean(AgreementFilesService::class)
    fun agreementFilesService() = agreementFilesService

    private val agreementFilesService: AgreementFilesService =
        spyk(
            AgreementFilesService(
                contractWriterService = contractWriterService,
                handlebarTemplateCompiler = mockk(),
                pdfConverter = mockk(),
            ),
        ) {
            every {
                createDeclarationOfResidency(any(), any(), any())
            } returns Unit
        }

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val dynamoDbEnhancedClient = DynamoDbEnhancedClient.builder()
        .dynamoDbClient(software.amazon.awssdk.services.dynamodb.DynamoDbClient.builder()
            .endpointOverride(java.net.URI.create("http://localhost:8000"))
            .region(software.amazon.awssdk.regions.Region.US_EAST_1)
            .credentialsProvider(software.amazon.awssdk.auth.credentials.StaticCredentialsProvider.create(
                software.amazon.awssdk.auth.credentials.AwsBasicCredentials.create("test", "test")
            ))
            .build())
        .build()
    private val modattaSimpleSignUpDynamoDAO = ModattaSimpleSignUpDynamoDAO(dynamoDbEnhancedClient)
    private val modattaSimpleSignUpDbRepository = ModattaSimpleSignUpDbRepository(modattaSimpleSignUpDynamoDAO, dynamoDbDAO)

    @MockBean(ObjectRepository::class)
    fun objectRepository() = objectRepository

    private val objectRepository: ObjectRepository =
        mockk {
            every {
                putObject(any(), any(), any())
            } just Runs
        }

    private val accountRepository = AccountDbRepository(dynamoDbDAO = dynamoDbDAO)

    private val expectedUserDataTO =
        UserDataTO(
            name = "name",
            birthDate = "1975-11-08",
            document = "***********",
            mobilePhone = "+*************",
            email = "<EMAIL>",
            userId = "4828265b-6fee-4101-8b3a-3aa039891499",
        )

    private val expectedLivenessId = LivenessId("LIVENESS-ID")

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)

        every {
            livenessService.enroll(accountId = ACCOUNT.accountId)
        } returns expectedLivenessId.right()

        every {
            accountService.findAccountById(any())
        } returns
            ACCOUNT.copy(
                configuration =
                ACCOUNT.configuration.copy(
                    externalId =
                    ExternalId(
                        ACCOUNT_ID_2,
                        AccountProviderName.MODATTA,
                    ),
                ),
            )

        every {
            accountService.createAccountPaymentMethod(any(), any(), any(), any())
        } returns externalAccountPayment
    }

    @Test
    fun `deve retornar NOT FOUND quando usuario nao comecou o cadastro`() {
        val request = buildGet()

        val thrown =
            assertThrows<HttpClientResponseException> {
                client.toBlocking()
                    .exchange(request, Argument.of(ModattaSimpleSignUpTO::class.java), Argument.of(ResponseTO::class.java))
            }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            this.code shouldBe "4004"
            this.message shouldBe "Account Register not found"
        }
    }

    @Test
    fun `deve retornar user data preenchido apos usuario compartilhar os dados`() {
        val request = buildPostUserData(expectedUserDataTO)

        val response =
            client.toBlocking()
                .exchange(request, Argument.of(ModattaSimpleSignUpTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK

        with(response.body.get()) {
//            userData shouldBe expectedUserDataTO
            enrollment?.livenessId shouldBe expectedLivenessId.value
            enrollment?.done shouldBe false
            userContract shouldBe null
            termsOfUse shouldBe agreementFileUrl
            validationStatus shouldBe ModattaSimpleSignUpValidationStatus.STARTED.name
        }

        verify {
            livenessService.enroll(ACCOUNT.accountId)
        }
    }

    @Test
    fun `deve manter o mesmo livenessId se usuario corrigir os dados`() {
        val request = buildPostUserData(expectedUserDataTO)

        modattaSimpleSignUpDbRepository.save(
            simpleSignUp =
            ModattaSimpleSignUp(
                userId = UserId(ACCOUNT.accountId.value),
                userData =
                UserData(
                    name = "to be updated",
                    birthDate = LocalDate.EPOCH,
                    document = Document("***********"),
                    mobilePhone = MobilePhone(msisdn = "+*************"),
                    email = EmailAddress(email = "<EMAIL>"),
                ),
                enrollment = Enrollment(livenessId = LivenessId(value = "OLD-LIVENESS-ID"), done = true),
                userContract = null,
                validationStatus = ModattaSimpleSignUpValidationStatus.STARTED,
            ),
        )

        val response =
            client.toBlocking()
                .exchange(request, Argument.of(ModattaSimpleSignUpTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK

        with(response.body.get()) {
//            userData shouldBe expectedUserDataTO
            enrollment?.livenessId shouldBe "OLD-LIVENESS-ID"
            enrollment?.done shouldBe true
            userContract shouldBe null
            termsOfUse shouldBe agreementFileUrl
            validationStatus shouldBe ModattaSimpleSignUpValidationStatus.STARTED.name
        }

        verify(exactly = 0) {
            livenessService.enroll(ACCOUNT.accountId)
        }
    }

    @ParameterizedTest
    @MethodSource("partnerSimpleSignUpValidation")
    fun `deve retornar liveness check done apos usuario completar fluxo de scan`(
        expectedStatus: ModattaSimpleSignUpValidationStatus,
        validation: PartnerSimpleSignUpValidation,
    ) {
        modattaSimpleSignUpService.updateUserData(
            UserId(ACCOUNT.accountId.value) as UserId,
            expectedUserDataTO.toUserData(),
        )

        val slot = slot<UserData>()
        every {
            partnerSimpleSignUpService.validate(capture(slot))
        } returns validation

        every {
            livenessService.retrieveEnrollmentSelfie(any())
        } returns ByteArray(0).right()

        val request = buildGet()

        val response =
            client.toBlocking()
                .exchange(request, Argument.of(ModattaSimpleSignUpTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK

        with(response.body.get()) {
//            userData shouldBe expectedUserDataTO
            enrollment?.livenessId shouldBe expectedLivenessId.value
            enrollment?.done shouldBe true // FIXME talvez devesse consultar o resultado do liveness e não acreditar somente na resposta do app modatta
            userContract shouldBe null
            termsOfUse shouldBe agreementFileUrl
            validationStatus shouldBe expectedStatus.name
        }

        with(slot.captured) {
            document.value shouldBe expectedUserDataTO.document
            mobilePhone.msisdn shouldBe expectedUserDataTO.mobilePhone
        }
    }

    @Test
    fun `deve criar a conta do usuário assim que ele enviar os dados mas não criar a carteira`() {
        val request = buildPostUserData(expectedUserDataTO)

        val existingAccount = accountRepository.findByIdOrNull(ACCOUNT.accountId)

        existingAccount.shouldBeNull()

        modattaSimpleSignUpDbRepository.save(
            simpleSignUp =
            ModattaSimpleSignUp(
                userId = UserId(ACCOUNT.accountId.value),
                userData =
                UserData(
                    name = "to be updated",
                    birthDate = LocalDate.EPOCH,
                    document = Document("***********"),
                    mobilePhone = MobilePhone(msisdn = "+*************"),
                    email = EmailAddress(email = "<EMAIL>"),
                ),
                enrollment = Enrollment(livenessId = LivenessId(value = "OLD-LIVENESS-ID"), done = true),
                userContract = null,
                validationStatus = ModattaSimpleSignUpValidationStatus.STARTED,
            ),
        )

        val response =
            client.toBlocking()
                .exchange(request, Argument.of(ModattaSimpleSignUpTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK

        val account = accountRepository.findByIdOrNull(ACCOUNT.accountId)

        account.shouldNotBeNull()
        account.status shouldBe AccountStatus.REGISTER_INCOMPLETE

        assertThrows<NoSuchElementException> { walletService.findPrimaryWallet(ACCOUNT.accountId) }
    }

    @Test
    fun `deve retornar aceito apos usuario concordar com termos de uso`() {
        val fridayExternalId =
            PartnerSimpleSignUpResult(
                accountId = AccountId("friday-account-id"),
            )

        every {
            partnerSimpleSignUpService.validate(any())
        } returns PartnerSimpleSignUpValidation(valid = true, accountId = null)

        every {
            livenessService.retrieveEnrollmentSelfie(any())
        } returns ByteArray(0).right()

        modattaSimpleSignUpService.updateUserData(
            UserId(ACCOUNT.accountId.value) as UserId,
            expectedUserDataTO.toUserData(),
        )

        val signUpSlot = slot<PartnerSimpleSignUpRequest>()
        every {
            partnerSimpleSignUpService.signUp(capture(signUpSlot))
        } returns fridayExternalId

        val cookie = buildModattaCookie(ACCOUNT)
        val request = buildPostAgreement(cookie)

        val now = ZonedDateTime.now()
        val response =
            withGivenDateTime(now) {
                client.toBlocking()
                    .exchange(request, Argument.of(ModattaSimpleSignUpTO::class.java), Argument.of(ResponseTO::class.java))
            }

        response.status shouldBe HttpStatus.OK

        val signatureSlot = slot<ContractSignature>()
        val formSlot = slot<ContractForm>()
        val storedObjectSlot = slot<StoredObject>()
        val inputStreamSlot = slot<InputStream>()
        verify(exactly = 1) {
            agreementFilesService.createContract(any(), any(), any())
            contractWriterService.createContract(any(), capture(formSlot), capture(signatureSlot))
            databaseObjectRepository.putObject(capture(storedObjectSlot), capture(inputStreamSlot))
        }
        signatureSlot.captured.signatureKey shouldNotBe null
        with(expectedUserDataTO) {
            formSlot.captured.fullName shouldBe name
            formSlot.captured.birthPlace shouldBe toUserData().document.issuerRegion
        }

        with(storedObjectSlot.captured) {
            region shouldBe userFilesConfiguration.region
            this.bucket shouldBe userFilesConfiguration.modattaUserDocumentsBucket
            this.key shouldBe "${userFilesConfiguration.path}/${ACCOUNT.accountId.value}/${userFilesConfiguration.contractPrefix}${now.toEpochSecond()}.pdf"
        }

        with(inputStreamSlot.captured) {
            String(this.readAllBytes()) shouldBe "mocked-byte-array"
        }

        with(signUpSlot.captured) {
            userData shouldBe expectedUserDataTO.toUserData()
            livenessId shouldBe expectedLivenessId
            externalId.value shouldBe ACCOUNT.accountId.value
            externalId.providerName shouldBe AccountProviderName.MODATTA
            userContractKey shouldBe storedObjectSlot.captured.key
            userContractSignature shouldBe signatureSlot.captured.signatureKey

            val account = accountRepository.findById(ACCOUNT.accountId)

            account.status shouldBe AccountStatus.UNDER_EXTERNAL_REVIEW
            account.configuration.externalId?.value shouldBe fridayExternalId.accountId.value
        }

        val simpleSignUp =
            modattaSimpleSignUpDbRepository.find(UserId(ACCOUNT.accountId.value)).getOrElse {
                throw IllegalStateException()
            }

        simpleSignUp.userContract.shouldNotBeNull()
        simpleSignUp.userContract?.hasAccepted shouldBe true
        simpleSignUp.userContract?.contract shouldBe storedObjectSlot.captured
    }

    @Test
    fun `deve retornar MISSING_USER_DATA na consulta de estado enquanto o usuario nao compartilhou os dados com a friday`() {
        val request = buildGetStatus()

        val response =
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ModattaSimpleSignUpStatusTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )

        response.status shouldBe HttpStatus.OK

        with(response.body.get()) {
            status shouldBe SimpleSignUpStatus.MISSING_USER_DATA
        }
    }

    @Test
    fun `deve retornar MISSING_ENROLLMENT na consulta de estado após compartilhar os dados e antes de terminar o enrollment`() {
        modattaSimpleSignUpDbRepository.save(
            simpleSignUp =
            ModattaSimpleSignUp(
                userId = UserId(ACCOUNT.accountId.value),
                userData =
                UserData(
                    name = "to be updated",
                    birthDate = LocalDate.EPOCH,
                    document = Document("***********"),
                    mobilePhone = MobilePhone(msisdn = "+*************"),
                    email = EmailAddress(email = "<EMAIL>"),
                ),
                enrollment = Enrollment(livenessId = LivenessId(value = "OLD-LIVENESS-ID"), done = false),
                userContract = null,
                validationStatus = ModattaSimpleSignUpValidationStatus.STARTED,
            ),
        )
        every {
            livenessService.retrieveEnrollmentSelfie(any())
        } returns LivenessSelfieError.Unavailable.left()

        val request = buildGetStatus()

        val response =
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ModattaSimpleSignUpStatusTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )

        response.status shouldBe HttpStatus.OK

        with(response.body.get()) {
            status shouldBe SimpleSignUpStatus.MISSING_ENROLLMENT
        }
    }

    @Test
    fun `deve retornar MISSING_AGREEMENT na consulta de estado após terminar o enrollment e antes de aceitar os termos`() {
        modattaSimpleSignUpDbRepository.save(
            simpleSignUp =
            ModattaSimpleSignUp(
                userId = UserId(ACCOUNT.accountId.value),
                userData =
                UserData(
                    name = "to be updated",
                    birthDate = LocalDate.EPOCH,
                    document = Document("***********"),
                    mobilePhone = MobilePhone(msisdn = "+*************"),
                    email = EmailAddress(email = "<EMAIL>"),
                ),
                enrollment = Enrollment(livenessId = LivenessId(value = "OLD-LIVENESS-ID"), done = true),
                userContract =
                UserContract(
                    hasAccepted = false,
                    contract = mockk<StoredObject>(relaxed = true),
                ),
                validationStatus = ModattaSimpleSignUpValidationStatus.VALID_WITH_PARTNER_ACCOUNT,
            ),
        )
        every {
            livenessService.retrieveEnrollmentSelfie(any())
        } returns ByteArray(0).right()

        val request = buildGetStatus()

        val response =
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ModattaSimpleSignUpStatusTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )

        response.status shouldBe HttpStatus.OK

        with(response.body.get()) {
            status shouldBe SimpleSignUpStatus.MISSING_AGREEMENT
        }
    }

    @Test
    fun `deve retornar INVALID_PHONE_AND_DOCUMENT na consulta de estado se falhar a validacao de cpf e telefone apos o enrollment`() {
        modattaSimpleSignUpDbRepository.save(
            simpleSignUp =
            ModattaSimpleSignUp(
                userId = UserId(ACCOUNT.accountId.value),
                userData =
                UserData(
                    name = "to be updated",
                    birthDate = LocalDate.EPOCH,
                    document = Document("***********"),
                    mobilePhone = MobilePhone(msisdn = "+*************"),
                    email = EmailAddress(email = "<EMAIL>"),
                ),
                enrollment = Enrollment(livenessId = LivenessId(value = "OLD-LIVENESS-ID"), done = true),
                userContract = UserContract(hasAccepted = true, contract = mockk<StoredObject>(relaxed = true)),
                validationStatus = ModattaSimpleSignUpValidationStatus.INVALID,
            ),
        )

        val request = buildGetStatus()

        val response =
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ModattaSimpleSignUpStatusTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )

        response.status shouldBe HttpStatus.OK

        with(response.body.get()) {
            status shouldBe SimpleSignUpStatus.INVALID_PHONE_AND_DOCUMENT
        }
    }

    @ParameterizedTest
    @EnumSource(
        ModattaSimpleSignUpValidationStatus::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["VALID_WITH_PARTNER_ACCOUNT", "VALID_WITHOUT_PARTNER_ACCOUNT"],
    )
    fun `deve retornar UNDER_REVIEW apos o enrollment com cpf e telefone validos e o aceite dos termos`(validationStatus: ModattaSimpleSignUpValidationStatus) {
        modattaSimpleSignUpDbRepository.save(
            simpleSignUp =
            ModattaSimpleSignUp(
                userId = UserId(ACCOUNT.accountId.value),
                userData =
                UserData(
                    name = "to be updated",
                    birthDate = LocalDate.EPOCH,
                    document = Document("***********"),
                    mobilePhone = MobilePhone(msisdn = "+*************"),
                    email = EmailAddress(email = "<EMAIL>"),
                ),
                enrollment = Enrollment(livenessId = LivenessId(value = "OLD-LIVENESS-ID"), done = true),
                userContract = UserContract(hasAccepted = true, contract = mockk<StoredObject>(relaxed = true)),
                validationStatus = validationStatus,
            ),
        )

        every { partnerSimpleSignUpService.getAccountStatus(any()) } returns AccountStatus.UNDER_REVIEW

        val request = buildGetStatus()

        val response =
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ModattaSimpleSignUpStatusTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )

        response.status shouldBe HttpStatus.OK

        with(response.body.get()) {
            status shouldBe SimpleSignUpStatus.UNDER_REVIEW
        }
    }

    @ParameterizedTest
    @EnumSource(AccountStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["BLOCKED", "PENDING_CLOSE"])
    fun `deve retornar o status correto apos o enrollment com cpf e telefone validos e o aceite dos termos`(
        accountStatus: AccountStatus,
    ) {
        modattaSimpleSignUpDbRepository.save(
            simpleSignUp =
            ModattaSimpleSignUp(
                userId = UserId(ACCOUNT.accountId.value),
                userData =
                UserData(
                    name = "to be updated",
                    birthDate = LocalDate.EPOCH,
                    document = Document("***********"),
                    mobilePhone = MobilePhone(msisdn = "+*************"),
                    email = EmailAddress(email = "<EMAIL>"),
                ),
                enrollment = Enrollment(livenessId = LivenessId(value = "OLD-LIVENESS-ID"), done = true),
                userContract = UserContract(hasAccepted = true, contract = mockk<StoredObject>(relaxed = true)),
                validationStatus = ModattaSimpleSignUpValidationStatus.VALID_WITH_PARTNER_ACCOUNT,
            ),
        )

        every { partnerSimpleSignUpService.getAccountStatus(any()) } returns accountStatus

        val request = buildGetStatus()

        val response =
            client.toBlocking()
                .exchange(
                    request,
                    Argument.of(ModattaSimpleSignUpStatusTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )

        response.status shouldBe HttpStatus.OK

        val statusMap =
            when (accountStatus) {
                AccountStatus.ACTIVE -> SimpleSignUpStatus.ACTIVE
                AccountStatus.CLOSED -> SimpleSignUpStatus.CLOSED
                AccountStatus.DENIED -> SimpleSignUpStatus.DENIED
                AccountStatus.APPROVED, AccountStatus.UNDER_REVIEW, AccountStatus.UNDER_EXTERNAL_REVIEW -> SimpleSignUpStatus.UNDER_REVIEW
                AccountStatus.REGISTER_INCOMPLETE -> SimpleSignUpStatus.REOPENED
                AccountStatus.BLOCKED -> throw IllegalStateException("should not happen")
                AccountStatus.PENDING_CLOSE -> throw IllegalStateException("should not happen")
            }

        with(response.body.get()) {
            status shouldBe statusMap
        }
    }

    private fun buildGet(): MutableHttpRequest<ModattaSimpleSignUpTO> {
        return HttpRequest.GET<ModattaSimpleSignUpTO>("/simpleSignUp/${ACCOUNT.accountId.value.removePrefix("ACCOUNT-")}")
            .header("Forwarded", "by=***********;for=$clientIP;host=127.0.0.1;proto=http")
            .basicAuth(modattaCallbackIdentity, modattaCallbackSecret)
    }

    private fun buildGetStatus(): MutableHttpRequest<ModattaSimpleSignUpStatusTO> {
        return HttpRequest.GET<ModattaSimpleSignUpStatusTO>(
            "/simpleSignUp/${ACCOUNT.accountId.value.removePrefix("ACCOUNT-")}/status",
        ).header("Forwarded", "by=***********;for=$clientIP;host=127.0.0.1;proto=http")
            .basicAuth(modattaCallbackIdentity, modattaCallbackSecret)
    }

    private fun buildPostUserData(userDataTO: UserDataTO): MutableHttpRequest<UserDataTO> {
        return HttpRequest.POST("/simpleSignUp/userData", userDataTO)
            .header("Forwarded", "by=***********;for=$clientIP;host=127.0.0.1;proto=http")
            .basicAuth(modattaCallbackIdentity, modattaCallbackSecret)
    }

    private fun buildPostAgreement(
        cookie: Cookie,
    ): MutableHttpRequest<Unit> {
        return HttpRequest.POST("/simpleSignUp/agreement", Unit)
            .cookie(cookie)
    }

    companion object {
        @JvmStatic
        fun partnerSimpleSignUpValidation(): Stream<Arguments> =
            Stream.of(
                Arguments.arguments(
                    ModattaSimpleSignUpValidationStatus.INVALID,
                    PartnerSimpleSignUpValidation(valid = false, accountId = null),
                ),
                Arguments.arguments(
                    ModattaSimpleSignUpValidationStatus.VALID_WITH_PARTNER_ACCOUNT,
                    PartnerSimpleSignUpValidation(valid = true, accountId = ACCOUNT.accountId),
                ),
                Arguments.arguments(
                    ModattaSimpleSignUpValidationStatus.VALID_WITHOUT_PARTNER_ACCOUNT,
                    PartnerSimpleSignUpValidation(valid = true, accountId = null),
                ),
            )
    }
}*/