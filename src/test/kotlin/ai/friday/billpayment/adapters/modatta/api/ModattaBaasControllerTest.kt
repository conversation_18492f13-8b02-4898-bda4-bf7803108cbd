package ai.friday.billpayment.adapters.modatta.api

import ai.friday.billpayment.adapters.arbi.ArbiAdapter
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.integrations.PixCommand
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.payment.PixPaymentResult
import ai.friday.billpayment.app.payment.PixPaymentStatus
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.mockk.every
import io.mockk.mockk
import java.math.BigInteger
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class ModattaBaasControllerTest {
    private val arbiAdapter: ArbiAdapter = mockk()
    private val pixPaymentAdapter: PixPaymentService = mockk()

    private val deviceFingerprintService = mockk<DeviceFingerprintService> {
        every { getInternalDevicesAccount() } returns mockk {
            every { deviceIds } returns mapOf(AccountNumber("123") to DeviceId("123"))
        }
    }

    private val modattaBaasController = ModattaBaasController(
        arbiAdapter = arbiAdapter,
        arbiPixPaymentAdapter = pixPaymentAdapter,
        configuration = BaasConfiguration(
            name = "Modatta",
            document = DOCUMENT,
            bankNo = 123,
            routingNo = 123,
            allowedIps = listOf("*******"),
        ),
        deviceFingerprintService = deviceFingerprintService,
    )

    private val uuid = UUID.randomUUID()

    private val pixTO = ModattaCreatePixTO(
        operationId = uuid.toString(),
        amount = 100,
        description = "Modatta pix",
        recipientPixKey = PixKey(value = DOCUMENT, type = PixKeyType.CPF),
        recipientName = "John Doe",
        recipientDocument = DOCUMENT,
        recipientBankAccount = null,
        originBankAccountDv = "1",
        originBankAccountNo = *********,
    )

    private val pixKeyDetails = PixKeyDetails(
        key = PixKey(value = "<EMAIL>", type = PixKeyType.EMAIL),
        holder = PixKeyHolder(
            accountNo = BigInteger("1127"),
            accountDv = "mollis",
            ispb = "maximus",
            institutionName = "Jarrett Moody",
            accountType = AccountType.CHECKING,
            routingNo = 7082,
        ),
        owner = PixKeyOwner(name = "Gerardo Lancaster", document = "***********"),
    )

    private val pixPaymentResult = PixPaymentResult(
        pixKeyDetails = PixKeyDetails(
            key = pixTO.recipientPixKey!!,
            holder = PixKeyHolder(
                accountNo = BigInteger("0"),
                accountDv = "",
                ispb = "",
                institutionName = "",
                accountType = AccountType.PAYMENT,
                routingNo = 0,
            ),
            owner = PixKeyOwner(name = "Jane Doe", document = "***********"),
        ),
        status = PixPaymentStatus.SUCCESS,
        idOrdemPagamento = "",
        endToEnd = "",
        error = null,
    )

    @BeforeEach
    fun setUp() {
        every {
            pixPaymentAdapter.initPayment(command = any<PixCommand>(), bankOperationId = any(), deviceId = null)
        } returns pixPaymentResult
    }

    @Test
    fun `deve fazer um pix com chave de uma conta modatta para uma conta de usuário`() {
        val response = modattaBaasController.createPix(body = pixTO)

        response.status shouldBe HttpStatus.CREATED
    }

    @Test
    fun `deve fazer um pix com dados de conta bancaria de uma conta modatta para uma conta de usuário`() {
        val response = modattaBaasController.createPix(
            body = pixTO.copy(
                recipientPixKey = null,
                recipientBankAccount = BankAccount(
                    accountType = AccountType.PAYMENT,
                    bankNo = 123,
                    routingNo = 2,
                    accountNo = BigInteger("********"),
                    accountDv = "1",
                    document = DOCUMENT_2,
                    ispb = null,
                ),
            ),
        )

        response.status shouldBe HttpStatus.CREATED
    }

    @Test
    fun `deve retornar erro caso haja algum problema em fazer o pix`() {
        every {
            pixPaymentAdapter.initPayment(command = any<PixCommand>(), bankOperationId = any(), deviceId = null)
        } throws HttpClientResponseException("erro", HttpResponse.serverError(Unit))

        val response = modattaBaasController.createPix(body = pixTO)

        response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
    }

    @ParameterizedTest
    @EnumSource(PixPaymentStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["UNKNOWN"])
    fun `deve retornar SUCCESS para uma consulta de status de pix`(status: PixPaymentStatus) {
        val bankOperationId = BankOperationId(uuid.toString())
        every {
            pixPaymentAdapter.checkPaymentStatus(any(), bankOperationId)
        } returns PixPaymentResult(
            pixKeyDetails = pixKeyDetails,
            status = status,
            idOrdemPagamento = uuid.toString(),
            endToEnd = "E2E",
            error = null,
        )

        val response = modattaBaasController.checkStatusPix(operationId = bankOperationId.value)

        response.status shouldBe HttpStatus.OK
        response.body() shouldBe status.name
    }

    @Test
    fun `deve retornar server error para uma consulta de status de pix com status UNKNOWN`() {
        val bankOperationId = BankOperationId(uuid.toString())
        every {
            pixPaymentAdapter.checkPaymentStatus(any(), bankOperationId)
        } returns PixPaymentResult(
            pixKeyDetails = pixKeyDetails,
            status = PixPaymentStatus.UNKNOWN,
            idOrdemPagamento = uuid.toString(),
            endToEnd = "E2E",
            error = null,
        )

        val response = modattaBaasController.checkStatusPix(operationId = bankOperationId.value)

        response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
    }
}