package ai.friday.billpayment.adapters.celcoin

import ai.friday.billpayment.app.payment.BillValidationStatus
import ai.friday.billpayment.app.payment.BoletoSettlementException
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.concessionariaBill
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.getPaidBill
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.reactivex.Flowable
import java.text.DecimalFormat
import java.text.NumberFormat
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Locale
import java.util.stream.Stream
import kotlin.random.Random
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource

internal class CelcoinAdapterTest {
    private val mockClient: RxHttpClient = mockk()
    private lateinit var celcoinAdapter: CelcoinAdapter
    private val celcoinAuth: CelcoinAuth = mockk(relaxed = true)

    @BeforeEach
    fun setUp() {
        val celcoinPaymentResponse = CelcoinPaymentResponse()
        val config = setupCelcoinConfig()
        celcoinAdapter = CelcoinAdapter(httpClient = mockClient, configuration = config, celcoinAuth)
        val celcoinAuthorizeResponseTO = CelcoinAuthorizeResponseTO(
            assignor = "BRADESCO SA",
            errorCode = "000",
            message = "Success",
            dueDate = "2019-11-06T12:23:04.914Z",
            settleDate = "06/11/2019",
            transactionId = 0L,
            value = 155.65,
            registerData = CelcoinRegisterDataTO(
                dueDateRegister = "2019-11-06T12:23:04",
                payDueDate = getZonedDateTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                originalValue = 155.65,
                documentRecipient = "31.872.495/0001-72",
            ),
        )
        val receipt = CelcoinTransactionReceipt(
            createDate = "2020-02-20",
            transactionId = "transactionId",
            receiptformatted = "formatted",
            authenticationAPI = CelcoinAuthenticationAPI(blocoCompleto = ""),
        )
        clearMocks(mockClient)
        every {
            mockClient.exchange(
                ofType(HttpRequest::class),
                ofType(Argument::class),
            )
        } answers { Flowable.just(HttpResponse.ok(celcoinAuthorizeResponseTO)) }
        every {
            mockClient.exchange(
                ofType(HttpRequest::class),
                CelcoinPaymentResponse::class.java,
            )
        } answers { Flowable.just(HttpResponse.ok(celcoinPaymentResponse)) }
        every { mockClient.exchange(ofType(HttpRequest::class), String::class.java) } answers {
            Flowable.just(
                HttpResponse.ok(celcoinPaymentResponse.toString()),
            )
        }
        every {
            mockClient.exchange(
                ofType(HttpRequest::class),
                CelcoinTransactionReceipt::class.java,
            )
        } answers { Flowable.just(HttpResponse.ok(receipt)) }
    }

    @Test
    fun `deve devolver a dueDate caso exista na concessionária`() {
        val bill = getActiveBill()
        val celcoinAuthorizeResponseTO = CelcoinAuthorizeResponseTO(
            assignor = "RO ICMS/IPVA/OUT RECEITAS",
            errorCode = "000",
            message = "Success",
            type = 1,
            dueDate = "2023-03-09T00:00:00Z",
            settleDate = "09/03/2023",
            transactionId = 1448622037L,
            value = 185.58,
            registerData = null,
        )

        every {
            mockClient.exchange(
                ofType(HttpRequest::class),
                ofType(Argument::class),
            )
        } answers { Flowable.just(HttpResponse.ok(celcoinAuthorizeResponseTO)) }

        val response = celcoinAdapter.validateBill(bill)

        response.billRegisterData!!.dueDate!!.shouldBeEqualComparingTo(LocalDate.parse("2023-03-09"))
    }

    @Test
    fun validateBill() {
        val bill = getPaidBill()
        val response = celcoinAdapter.validateBill(bill)
        Assertions.assertTrue(response.isValid())
        Assertions.assertEquals(15565, response.billRegisterData!!.amount)
        Assertions.assertEquals("31872495000172", response.billRegisterData!!.recipient!!.document)
        verify { mockClient.exchange(ofType(HttpRequest::class), ofType(Argument::class)) }
    }

    @Test
    fun `deve fazer decode do erro adequadamente`() {
        val celcoinErrorResponse = CelcoinErrorResponse("ERROR_CODE", "MESSAGE")

        every {
            mockClient.exchange(ofType(HttpRequest::class), ofType(Argument::class))
        } throws HttpClientResponseException(
            "TestError",
            HttpResponse.badRequest("{\"errorCode\": \"ERROR_CODE\", \"message\": \"MESSAGE\"}"),
        )

        celcoinAdapter.validateBill(getPaidBill()).run {
            CelcoinBillValidationResponse(celcoinErrorResponse.errorCode, celcoinErrorResponse.message)
        }

        clearMocks(mockClient)
    }

    @Test
    fun `deve fazer decode do erro adequadamente quando boleto já tiver sido pago`() {
        val celcoinErrorResponse = CelcoinErrorResponse("183", "Pagamento ja efetuado.")

        every {
            mockClient.exchange(ofType(HttpRequest::class), ofType(Argument::class))
        } throws HttpClientResponseException("Bad request", HttpResponse.badRequest("{\"errorCode\":\"183\",\"message\":\"Pagamento ja efetuado.\"}"))

        celcoinAdapter.validateBill(getPaidBill()).run {
            CelcoinBillValidationResponse(celcoinErrorResponse.errorCode, celcoinErrorResponse.message)
        }

        clearMocks(mockClient)
    }

    @Test
    fun `deve lançar exception se o response body é nulo`() {
        every {
            mockClient.exchange(ofType(HttpRequest::class), ofType(Argument::class))
        } returns Flowable.just(HttpResponse.ok())

        val ex = assertThrows<BoletoSettlementException> { celcoinAdapter.validateBill(getPaidBill()) }
        ex.cause?.message shouldBe "body must not be null"

        clearMocks(mockClient)
    }

    @Test
    fun `boleto com valor negativo não deve retornar com sucesso`() {
        every {
            mockClient.exchange(
                ofType(HttpRequest::class),
                ofType(Argument::class),
            )
        } answers {
            Flowable.just(
                HttpResponse.ok(
                    CelcoinAuthorizeResponseTO(
                        assignor = "BRADESCO SA",
                        errorCode = "000",
                        message = "Success",
                        dueDate = "2019-11-06T12:23:04.914Z",
                        settleDate = "06/11/2019",
                        transactionId = 0L,
                        value = -155.65,
                        registerData = CelcoinRegisterDataTO(
                            dueDateRegister = "2019-11-06T12:23:04",
                            payDueDate = getZonedDateTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                            totalUpdated = -155.65,
                            documentRecipient = "31.872.495/0001-72",
                        ),
                    ),
                ),
            )
        }
        val response = celcoinAdapter.validateBill(concessionariaBill)

        response.isValid().shouldBeFalse()
        response.getStatus().shouldBeTypeOf<BillValidationStatus.UnableToValidate>()
        response.isRetryable().shouldBeTrue()
    }

    @ParameterizedTest
    @MethodSource("valuesProvider")
    fun convertTolong(value: Double, expectedValue: Long) {
        celcoinAdapter.convertToLong(value.toString()) shouldBe expectedValue
    }

    @Test
    fun convertTolong() {
        celcoinAdapter.convertToLong(235238.29.toString()) shouldBe 23523829L
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "000,0,CONFIRMED",
            "000,1,VOIDED",
            "000,4,AUTHORIZED",
            "061,1,UNAUTHORIZED",
            "999,9,UNKNOWN",
        ],
    )
    fun queryPayment(errorCode: String, status: Int, settlementStatus: BoletoSettlementStatus) {
        every {
            mockClient.exchange(
                ofType(HttpRequest::class),
                ofType(Argument::class),
                ofType(Argument::class),
            )
        } answers {
            Flowable.just(
                HttpResponse.ok(
                    CelcoinQueryPaymentResponseTO(
                        transaction = CelcoinTransactionTO(
                            authentication = 7429,
                            errorCode = errorCode,
                            createDate = "2022-05-06T15:23:49",
                            message = "SUCESSO",
                            externalNSU = 101,
                            transactionId = *********,
                            status = status,
                            externalTerminal = "ACCOUNT-8e8d8d4c-0a3f-4d4f-ac30-2025954cc9e8",
                        ),
                        errorCode = "000",
                        message = "SUCESSO",
                        status = 0,
                    ),
                ),
            )
        }
        val response = celcoinAdapter.queryPayment("0000000")
        response shouldBe settlementStatus
    }

    private fun setupCelcoinConfig(): CelcoinAdapterConfiguration = CelcoinAdapterConfiguration().apply {
        authorizePath = "/path/to/authorize"
        paymentPath = "/path/to/payment"
        capturePath = "/path/{transactionId}/capture"
        voidPath = "/path/{transactionId}/void"
        queryPath = "/path/{transactionId}/query"
        receiptPath = "/path/receipt/{transactionId}"
        bankTransferPath = ""
        host = ""
        pendencyPath = ""
        bankTransferStatusPath = ""
        banksPath = ""
        username = "username"
        password = "password"
    }

    companion object {
        @JvmStatic
        fun valuesProvider(): Stream<Arguments> {
            val nf = NumberFormat.getNumberInstance(Locale.US) as DecimalFormat
            nf.applyPattern("#.##")
            return (1..25).map {
                val randomNumber = Random.nextLong(0L, 100000000L)
                Arguments.arguments(nf.format(randomNumber / 100.0).toDouble(), randomNumber)
            }.stream()
        }
    }
}