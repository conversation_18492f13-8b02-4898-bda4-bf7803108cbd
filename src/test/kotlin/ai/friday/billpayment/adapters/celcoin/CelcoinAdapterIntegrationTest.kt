package ai.friday.billpayment.adapters.celcoin
//
// import ai.friday.billpayment.app.FRIDAY_ENV
// import arrow.core.right
// import io.kotest.matchers.shouldBe
// import io.micronaut.test.extensions.junit5.annotation.MicronautTest
// import org.junit.jupiter.api.Disabled
// import org.junit.jupiter.api.Test
//
// @Disabled
// @MicronautTest(environments = [FRIDAY_ENV])
// internal class CelcoinAdapterIntegrationTest(private val celcoinAdapter: CelcoinAdapter) {
//
//    @Test
//    fun getBalanceAmount() {
//        celcoinAdapter.getBalanceAmount() shouldBe 299974724441L.right()
//    }
// }