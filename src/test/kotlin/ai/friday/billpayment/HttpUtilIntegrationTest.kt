package ai.friday.billpayment

import ai.friday.billpayment.app.FRIDAY_ENV
import arrow.core.getOrElse
import io.kotest.matchers.ints.shouldBeGreaterThan
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import java.net.URI
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail

@MicronautTest(environments = [FRIDAY_ENV])
@Property(name = "micronaut.security.enabled", value = "false")
internal class HttpUtilIntegrationTest(private val embeddedServer: EmbeddedServer) {
    @Controller
    class HttpUtilTestController {
        @Get("/get")
        fun get(request: HttpRequest<*>): MutableHttpResponse<TestHttpResponse> = HttpResponse.ok(request.asBody())

        @Post("/post")
        fun post(
            request: HttpRequest<*>,
            @Body body: FooRequest,
        ): MutableHttpResponse<TestHttpResponse> = HttpResponse.ok(request.asBody(body))

        @Put("/put")
        fun put(
            request: HttpRequest<*>,
            @Body body: FooRequest,
        ): MutableHttpResponse<TestHttpResponse> = HttpResponse.ok(request.asBody(body))

        @Delete("/delete")
        fun delete(request: HttpRequest<*>, @Body body: FooRequest): MutableHttpResponse<TestHttpResponse> =
            HttpResponse.ok(request.asBody(body))

        private fun <I> HttpRequest<I>.asBody(requestBody: FooRequest? = null) =
            TestHttpResponse(this.headers.asMap(), this.uri, BarResponse(requestBody))
    }

    private lateinit var cli: RxHttpClient

    @BeforeEach
    internal fun setUp() {
        cli = RxHttpClient.create(embeddedServer.url)
    }

    @Test
    internal fun `should do basic get request`() {
        val response = cli.get<TestHttpResponse>("/get").getOrElse { fail(it.toString()) }

        with(response) {
            requestURI shouldBe URI("/get")

            requestHeaders.assertHeaders()

            requestBody shouldBe BarResponse()
        }
    }

    @Test
    internal fun `should do get request with query parameters`() {
        val response = cli.get<TestHttpResponse>("/get", qs = mapOf("foo" to "bar", "ham" to 1))
            .getOrElse { fail(it.toString()) }

        with(response) {
            requestURI shouldBe URI("/get?foo=bar&ham=1")

            requestHeaders.assertHeaders()

            requestBody shouldBe BarResponse()
        }
    }

    @Test
    internal fun `should do get request with custom headers`() {
        val response =
            cli.get<TestHttpResponse>("/get", headers = mapOf("foo" to "bar")).getOrElse { fail(it.toString()) }

        with(response) {
            requestURI shouldBe URI("/get")

            requestHeaders.assertHeaders("foo" to "bar")

            requestBody shouldBe BarResponse()
        }
    }

    @Test
    internal fun `should do get request changing default headers`() {
        val response = cli.get<TestHttpResponse>("/get", headers = mapOf("foo" to "bar")) {
            return@get it.contentType(MediaType.APPLICATION_FORM_URLENCODED_TYPE)
        }.getOrElse { fail(it.toString()) }

        with(response) {
            requestURI shouldBe URI("/get")

            requestHeaders.assertHeaders("foo" to "bar", "Content-Type" to MediaType.APPLICATION_FORM_URLENCODED)

            requestBody shouldBe BarResponse()
        }
    }

    @Test
    internal fun `should do basic get exchange request`() {
        val response = cli.getHttp<TestHttpResponse>("/get").getOrElse { fail(it.toString()) }

        response.status shouldBe HttpStatus.OK
        response.body().shouldNotBeNull()

        val body = response.body()!!

        with(body) {
            requestURI shouldBe URI("/get")

            requestHeaders.assertHeaders()

            requestBody shouldBe BarResponse()
        }
    }

    @Test
    internal fun `should do basic post request`() {
        val response =
            cli.post<TestHttpResponse>("/post", body = FooRequest()).getOrElse { fail(it.toString()) }

        with(response) {
            requestURI shouldBe URI("/post")

            requestHeaders.assertHeaders()

            requestBody shouldBe BarResponse(FooRequest())
        }
    }

    @Test
    internal fun `should do post request with custom headers`() {
        val response =
            cli.post<TestHttpResponse>("/post", body = FooRequest(), headers = mapOf("foo" to "bar"))
                .getOrElse { fail(it.toString()) }

        with(response) {
            requestURI shouldBe URI("/post")

            requestHeaders.assertHeaders("foo" to "bar")

            requestBody shouldBe BarResponse(FooRequest())
        }
    }

    @Test
    internal fun `should do basic post exchange request`() {
        val response =
            cli.postHttp<TestHttpResponse>("/post", body = FooRequest()).getOrElse { fail(it.toString()) }

        response.status shouldBe HttpStatus.OK
        response.body().shouldNotBeNull()

        val body = response.body()!!

        with(body) {
            requestURI shouldBe URI("/post")

            requestHeaders.assertHeaders()

            requestBody shouldBe BarResponse(FooRequest())
        }
    }

    @Test
    internal fun `should do basic put request`() {
        val response =
            cli.put<TestHttpResponse>("/put", body = FooRequest()).getOrElse { fail(it.toString()) }

        with(response) {
            requestURI shouldBe URI("/put")

            requestHeaders.assertHeaders()

            requestBody shouldBe BarResponse(FooRequest())
        }
    }

    @Test
    internal fun `should do basic put exchange request`() {
        val response =
            cli.putHttp<TestHttpResponse>("/put", body = FooRequest()).getOrElse { fail(it.toString()) }
        response.status shouldBe HttpStatus.OK
        response.body().shouldNotBeNull()

        val body = response.body()!!

        with(body) {
            requestURI shouldBe URI("/put")

            requestHeaders.assertHeaders()

            requestBody shouldBe BarResponse(FooRequest())
        }
    }

    @Test
    internal fun `should do basic delete request`() {
        val response =
            cli.delete<TestHttpResponse>("/delete", body = FooRequest()).getOrElse { fail(it.toString()) }

        with(response) {
            requestURI shouldBe URI("/delete")

            requestHeaders.assertHeaders()

            requestBody shouldBe BarResponse(FooRequest())
        }
    }

    @Test
    internal fun `should do basic delete exchange request`() {
        val response =
            cli.deleteHttp<TestHttpResponse>("/delete", body = FooRequest())
                .getOrElse { fail(it.toString()) }

        response.status shouldBe HttpStatus.OK
        response.body().shouldNotBeNull()

        val body = response.body()!!

        with(body) {
            requestURI shouldBe URI("/delete")

            requestHeaders.assertHeaders()

            requestBody shouldBe BarResponse(FooRequest())
        }
    }

    @Test
    internal fun `should deal with error`() {
        val response = cli.post<TestHttpResponse>("/error", FooRequest())

        response.fold(
            {
                Assertions.assertTrue(it is HttpClientError)

                it.args.size shouldBe 1
                it.args["status"] shouldBe HttpStatus.NOT_FOUND
            },
            {
                fail("should fail with bad request error")
            },
        )
    }

    private fun Map<*, *>.assertHeaders(vararg args: Pair<String, Any>) {
        this.size shouldBeGreaterThan 0

        val entries = args.toMap()

        val contentType = entries["Content-Type"] ?: MediaType.APPLICATION_JSON
        val accept = entries["Accept"] ?: MediaType.APPLICATION_JSON

        this["Accept"] shouldBe listOf(accept)
        this["Content-Type"] shouldBe listOf(contentType)

        if (args.isNotEmpty()) {
            args.forEach {
                this[it.first].shouldNotBeNull()
                this[it.first] shouldBe listOf(it.second)
            }
        }
    }
}