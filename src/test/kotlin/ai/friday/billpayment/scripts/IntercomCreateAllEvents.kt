/*
package ai.friday.billpayment.scripts

import ai.friday.billpayment.adapters.intercom.IntercomAdapter
import ai.friday.billpayment.adapters.intercom.IntercomConfiguration
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.integrations.CrmContact
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.net.URL
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
class IntercomCreateAllEvents() {
    private val apiToken = "" // prod mepoupe

    private val intercomURL = "https://api.intercom.io"
    private val intercomClient = RxHttpClient.create(URL(intercomURL))
    private val intercomConfiguration = IntercomConfiguration().apply {
        host = intercomURL
        token = apiToken
    }
    private val intercomAdapter = IntercomAdapter(intercomClient, intercomConfiguration)

    private val accountId = AccountId("ACCOUNT-FAKE")

    private val contact = CrmContact(
        accountId = accountId,
        emailAddress = EmailAddress("<EMAIL>"),
        role = Role.OWNER, name = "nome do usuário fake", mobilePhone = MobilePhone("+*************"), document = "***********", removed = false, groups = listOf(AccountGroup.ALPHA), bankAccount = listOf("1234"), accountType = UserAccountType.BASIC_ACCOUNT, isCNPJAccount = false, cnpjWallets = listOf(), cpfWallets = listOf(WalletId("WALLET-FAKE")), otherMembersOnWallets = listOf(), subscriptionType = SubscriptionType.IN_APP, subscriptionAccessConcessionReason = InAppSubscriptionReason.BACKOFFICE, subscriptionEndsAt = null,
    )

    @Test
    fun createAllEvents() {
        intercomAdapter.upsertContact(contact)
        createAllFromBillInstrumentationRepository()
        createAllFromCashInInstrumentationRepository()
        createAllFromUtilityAccountService()
        createAllFromSubscriptionService()
        createAllFromRegisterInstrumentationRepository()
        createAllFromWalletInstrumentationRepository()
        createAllFromFrontend()
    }

    private fun createAllFromBillInstrumentationRepository() {
        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "conta_criada_por_email_de_usuario",
            customParams = mapOf(
                "memberAccountId" to accountId.value,
                "founderAccountId" to accountId.value,
                "senderEmailDomain" to "email_domain",
            ),
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "conta_criada_por_email_desconhecido",
            customParams = mapOf(
                "memberAccountId" to accountId.value,
                "founderAccountId" to accountId.value,
                "senderEmailDomain" to "email_domain",
            ),
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "conta_criada_com_recorrencia",
            customParams = mapOf(
                "memberAccountId" to accountId.value,
                "founderAccountId" to accountId.value,
                "billType" to "tipo da conta",
                "frequency" to "frequencia",
            ),
        )

        val customParams = mapOf(
            "memberAccountId" to accountId.value,
            "founderAccountId" to accountId.value,
            "billsCount" to 1,
        )
        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "conta_agendada",
            customParams = customParams,
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "conta_agendada_multipla",
            customParams = customParams,
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "conta_paga",
            customParams = mapOf(
                "founderAccountId" to accountId.value,
                "billType" to "tipo da conta",
            ),
        )
    }

    private fun createAllFromCashInInstrumentationRepository() {
        val customParams = mapOf(
            "type" to "CREDIT_CARD",
            "receiverAccountId" to accountId.value,
            "tier" to "BELOW_FIVE_HUNDRED",
            "sameOwnership" to true,
            "succeeded" to true,
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "cash_in_cartao",
            customParams = customParams,
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "cash_in_cartao_abaixo_de_quinhentos",
            customParams = customParams,
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "cash_in_cartao_acima_de_quinhentos",
            customParams = customParams,
        )

        val customParams2 = mapOf(
            "type" to "PIX",
            "receiverAccountId" to accountId.value,
            "tier" to "BELOW_FIVE_HUNDRED",
            "sameOwnership" to true,
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "cash_in_deposito",
            customParams = customParams2,
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "cash_in_deposito_abaixo_de_quinhentos",
            customParams = customParams2,
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "cash_in_deposito_acima_de_quinhentos",
            customParams = customParams2,
        )
    }

    private fun createAllFromUtilityAccountService() {
        intercomAdapter.publishEvent(
            accountId,
            "conta_de_consumo_conectada",
            mapOf("concessionaria" to "CONCESSIONARIA_FAKE", "walletId" to "WALLET_FAKE"),
        )
    }

    private fun createAllFromSubscriptionService() {
        intercomAdapter.publishEvent(accountId, "isencao_de_assinatura", mapOf("reason" to "motivo", "months" to "quantidad de meses"))
    }

    private fun createAllFromRegisterInstrumentationRepository() {
        val customParams = mapOf(
            "registrationType" to "FULL",
            "billsCount" to 1,
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "cadastro_ativado",
            customParams = customParams,
        )

        repeat(3) { index ->
            intercomAdapter.publishEvent(
                accountId = accountId,
                eventName = when (index) {
                    0 -> "cadastro_ativado_sem_contas"
                    1 -> "cadastro_ativado_com_uma_conta"
                    else -> "cadastro_ativado_com_varias_contas"
                },
                customParams = customParams,
            )
        }

        val customParams2 = mapOf(
            "registrationType" to "FULL",
        )
        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "cadastro_completo",
            customParams = customParams2,
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "cadastro_rejeitado_externamente",
            customParams = customParams,
        )

        val customParams3 = mapOf(
            "registrationType" to "BASIC",
            "deniedReason" to listOf("motivo").joinToString(separator = ", ") { it },
        )
        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "cadastro_rejeitado_internamente",
            customParams = customParams3,
        )
        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "cadastro_reaberto",
            customParams = customParams3,
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "cadastro_upgraded",
            customParams = customParams3,
        )

        val customParams4 = mutableMapOf(
            "accountType" to "BASIC_ACCOUNT",
            "reason" to "USER_REQUEST",
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "cadastro_closed",
            customParams = customParams4,
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "conta_upgraded",
        )
    }

    private fun createAllFromWalletInstrumentationRepository() {
        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "removido_da_carteira",
            customParams = mapOf(
                "walletName" to "nome da carteira",
                "removedBy" to "nome de quem removeu",
            ),
        )

        intercomAdapter.publishEvent(
            accountId = accountId,
            eventName = "membro_removido_da_carteira",
            customParams = mapOf(
                "walletName" to "nome da carteira",
                "removedMember" to "nome do membro removido",
            ),
        )
    }

    private fun createAllFromFrontend() {
        val eventNames = listOf(
            "account_blocked.subscription_banner_showed",
            "banner_de_conta_bloqueada_exibido",
            "account_blocked.subscription_banner_dismissed",
            "banner_de_conta_bloqueada_fechado",
            "account_blocked.subscription_banner_pix_qr_code_copied",
            "banner_de_conta_bloqueada_código_pix_copiado",
            "pagamento_recorrente_adicionado",
            "pagamento_recorrente_adicionado",
            "checkout_pagamento_revisado",
            "checkout_adicionar_mais_contas",
            "convidar_para_carteira_iniciado",
            "convidar_para_carteira_finalizado",
            "checkout_concluido_com_sucesso",
            "multiplas_contas_agendadas",
            "checkout_adicionar_mais_contas",
            "checkout_pagamento_revisado",
            "checkout_forma_de_pagamento_selecionada",
            "checkout_saldo_selecionado_como_metodo_de_pagamento",
            "checkout_cartao_de_credito_selecionado_como_metodo_de_pagamento",
            "timeline.viewed",
            "cash_in_formas_de_adicionar_visualizadas",
            "senha_criada",
            "cadastro_iniciado",
            "cadastro_retomado",
            "cadastro_reaberto_retomado",
            "cadastro_assinatura_realizada",
            "cadastro_dispositivo_nao_suporta_assinatura",
            "cadastro_informacao_pessoal_enviada",
            "cadastro_cpf_enviado",
            "cadastro_token_email_solicitado",
            "cadastro_email_sendo_verificado",
            "cadastro_email_verificado",
            "cadastro_email_nao_verificado",
            "cadastro_camera_nao_disponivel",
            "cadastro_documento_enviado",
            "cadastro_enviou_os_dados_do_documento",
            "cadastro_cep_nao_suportado",
            "cadastro_endereco_enviado",
            "cadastro_liveness_enviado",
            "cadastro_liveness_enviado_com_sucesso",
            "cadastro_liveness_enviado_com_falha",
            "cadastro_renda_mensal_enviada",
            "cadastro_ppe_enviada",
            "cadastro_upgrade_necessario",
            "cadastro_upgrade_nao_necessario",
            "cadastro_termos_de_uso_aceitos",
            "cadastro_completado",
            "cadastro_declaracao_residencia_baixada",
            "cadastro_termos_de_uso_baixado",
            "cadastro_contrato_baixado",
            "cadastro_pagina_final_vista",
            "cadastro_respondeu_pesquisa",
            "upgrade_iniciado",
            "upgrade_abandonado",
            "upgrade_selecionou_tipo_de_documento",
            "upgrade_documento_enviado",
            "upgrade_enviou_os_dados_do_documento",
            "upgrade_endereco_enviado",
            "upgrade_renda_mensal_enviada",
            "upgrade_declaracao_de_residencia_baixada",
            "upgrade_termos_de_uso_baixado",
            "upgrade_contrato_baixado",
            "upgrade_termos_de_uso_aceitos",
            "upgrade_completado",
        )

        eventNames.forEach { eventName ->
            intercomAdapter.publishEvent(accountId, eventName)
        }
    }
}*/