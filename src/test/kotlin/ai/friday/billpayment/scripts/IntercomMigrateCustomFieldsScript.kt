/*
package ai.friday.billpayment.scripts

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.net.URL
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
class IntercomMigrateCustomFieldsScript() {
    private val intercomURL = "https://api.intercom.io"
    private val intercomClient = RxHttpClient.create(URL(intercomURL))

    private val sourceToken = "" // prod Friday
    private val destinationToken = "" // prod mepoupe

    private val dryRun = false

    @Test
    fun importAllCustomFields() {
        val sourceFields = listAllCustomFields(sourceToken).filter { it.canBeImported() }
        val destinationFields = listAllCustomFields(destinationToken).filter { it.canBeImported() }
        val destinationFieldsNames = destinationFields.map { it.name }

        println("Total Source Fields: ${sourceFields.size}")
        println("Total Destination Fields: ${destinationFields.size}")

        val toBeCreated = sourceFields.filter { it.name !in destinationFieldsNames }
        println("Total fields to be imported: ${toBeCreated.size}")

        toBeCreated.forEach {
            print("Creating field: ${it.name} ... ")
            if (!dryRun) {
                try {
                    createCustomField(it, destinationToken)
                    println("OK CREATED")
                } catch (e: HttpClientResponseException) {
                    println("ERROR: ${e.response.getBody(Argument.of(String::class.java))}")
                    // throw e
                }
            } else {
                println("DRY RUN. NO CHANGE")
            }
        }
    }

    private fun listAllCustomFields(token: String): List<CustomFieldTO> {
        val request =
            HttpRequest.GET<Unit>("/data_attributes?include_archived=true&model=contact").bearerAuth(token)
                .accept(MediaType.APPLICATION_JSON_TYPE)
        val call =
            intercomClient.exchange(
                request,
                Argument.of(CustomFieldListTO::class.java),
            )
        return call.firstOrError().blockingGet().body().data
    }

    private fun createCustomField(customFieldTO: CustomFieldTO, token: String) {
        val toCreateCustomFieldTO = customFieldTO.toCreateCustomFieldTO()
        println(getObjectMapper().writeValueAsString(toCreateCustomFieldTO))
        val request = HttpRequest.POST(
            "/data_attributes",
            toCreateCustomFieldTO,
        ).bearerAuth(token)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = intercomClient.exchange(
            request,
            Argument.of(CustomFieldTO::class.java),
        )
        call.firstOrError().blockingGet()
    }
}

private fun CustomFieldTO.canBeImported(): Boolean {
    return true || this.data_type != "relationship"
}

private fun CustomFieldTO.toCreateCustomFieldTO(): CreateCustomFieldTO {
    return CreateCustomFieldTO(
        name = name,
        description = description,
        data_type = data_type,
        model = model,
        options = options?.map { CreateCustomFieldOptionTO(it) },
        messenger_writable = messenger_writable,
    )
}

private data class CreateCustomFieldTO(
    val name: String,
    val model: String,
    val data_type: String,
    val description: String?,
    val options: List<CreateCustomFieldOptionTO>?,
    val messenger_writable: Boolean?,
)

private data class CreateCustomFieldOptionTO(
    val value: String,
)

private data class CustomFieldListTO(
    val type: String,
    val data: List<CustomFieldTO>,
)

private data class CustomFieldTO(
    val type: String,
    val name: String,
    val full_name: String,
    val label: String,
    val description: String?,
    val data_type: String,
    val api_writable: Boolean,
    val ui_writable: Boolean,
    val custom: Boolean,
    val archived: Boolean,
    val model: String,
    val options: List<String>?,
    val messenger_writable: Boolean,
)*/