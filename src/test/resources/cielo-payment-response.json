{"MerchantOrderId": "2014111706", "Customer": {"Name": "Comprador crédito simples"}, "Payment": {"ServiceTaxAmount": 0, "Installments": 1, "Interest": "ByMerchant", "Capture": false, "Authenticate": false, "CreditCard": {"CardNumber": "455187******0183", "Holder": "<PERSON><PERSON>", "ExpirationDate": "12/2030", "SaveCard": false, "Brand": "Visa", "CardOnFile": {"Usage": "Used", "Reason": "Unscheduled"}}, "IsCryptoCurrencyNegotiation": true, "tryautomaticcancellation": true, "ProofOfSale": "674532", "Tid": "0305023644309", "AuthorizationCode": "123456", "PaymentId": "24bc8366-fc31-4d6c-8555-17049a836a07", "Type": "CreditCard", "Amount": 15700, "Currency": "BRL", "Country": "BRA", "ExtraDataCollection": [], "Status": 1, "ReturnCode": "4", "ReturnMessage": "Operation Successful", "Links": [{"Method": "GET", "Rel": "self", "Href": "https://apiquerysandbox.cieloecommerce.cielo.com.br/1/sales/{PaymentId}"}, {"Method": "PUT", "Rel": "capture", "Href": "https://apisandbox.cieloecommerce.cielo.com.br/1/sales/{PaymentId}/capture"}, {"Method": "PUT", "Rel": "void", "Href": "https://apisandbox.cieloecommerce.cielo.com.br/1/sales/{PaymentId}/void"}]}}