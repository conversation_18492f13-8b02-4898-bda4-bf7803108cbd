<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoders are assigned the type
             ch.qos.logback.classic.encoder.PatternLayoutEncoder by default -->
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <jsonGeneratorDecorator class="net.logstash.logback.mask.MaskingJsonGeneratorDecorator">
                <defaultMask>****</defaultMask>
                <path>tokenusuario</path>
            </jsonGeneratorDecorator>
        </encoder>
        <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
            <exclude>^io.netty.channel</exclude>
            <exclude>^io.netty.handler</exclude>
            <maxDepthPerThrowable>25</maxDepthPerThrowable>
        </throwableConverter>
    </appender>

    <logger name="io.micronaut.logging" level="OFF"/>
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />

    <!-- Just wrap your logging appender, for example ConsoleAppender, with OpenTelemetryAppender -->
    <!--    <appender name="OTEL" class="io.opentelemetry.instrumentation.logback.mdc.v1_0.OpenTelemetryAppender">-->
    <!--        <appender-ref ref="STDOUT"/>-->
    <!--    </appender>-->

    <!-- <logger name="io.micronaut.core.reflect" level="TRACE" /> -->
    <!-- Use the wrapped "OTEL" appender instead of the original "CONSOLE" one -->
    <root level="ERROR">
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>
