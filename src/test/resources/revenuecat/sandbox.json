{"event": {"event_timestamp_ms": *************, "product_id": "friday_990_1m_2w0", "period_type": "NORMAL", "purchased_at_ms": *************, "expiration_at_ms": *************, "environment": "SANDBOX", "entitlement_id": null, "entitlement_ids": ["classic"], "presented_offering_id": "sign_up_prices_a", "transaction_id": "****************", "original_transaction_id": "****************", "is_family_share": false, "country_code": "BR", "app_user_id": "ACCOUNT-547316e3-29d4-49b4-89f5-d6983c65a937", "aliases": ["ACCOUNT-547316e3-29d4-49b4-89f5-d6983c65a937"], "original_app_user_id": "ACCOUNT-547316e3-29d4-49b4-89f5-d6983c65a937", "currency": "BRL", "is_trial_conversion": false, "price": 1.804, "price_in_purchased_currency": 9.9, "subscriber_attributes": {"$ip": {"value": "*************", "updated_at_ms": *************}, "$idfa": {"value": "********-0000-0000-0000-************", "updated_at_ms": *************}, "$idfv": {"value": "995890A4-AF4E-41F8-B791-66FBAAB4D033", "updated_at_ms": *************}, "$adjustId": {"value": "50ea36f3c56fdef6d5399b87c71062d0", "updated_at_ms": *************}, "$firebaseAppInstanceId": {"value": "249EF5FAB5D24011BAC890B9FDDECF9D", "updated_at_ms": *************}, "$attConsentStatus": {"value": "denied", "updated_at_ms": *************}}, "store": "APP_STORE", "takehome_percentage": 0.7, "offer_code": null, "tax_percentage": 0.0, "commission_percentage": 0.3, "renewal_number": 25, "type": "RENEWAL", "id": "A95E4A57-AA3B-4C22-9ADF-0F94C4DF9367", "app_id": "app5857ed6394"}, "api_version": "1.0"}