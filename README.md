# bill-payment-service

[![pipeline status](https://gitlab.com/via1/bill-payment-service/badges/main/pipeline.svg)](https://gitlab.com/via1/bill-payment-service/commits/master)

Serviço de pagamento de contas do Via1

## Pre-requisites

- java 17 sdk (It is recomended to use [sdkman](https://sdkman.io) in order to manage java versions).
- docker (tests use docker)

```
sdk install java
java -version
```

- Configure [gitlab maven access](https://docs.gitlab.com/ee/user/packages/maven_repository/#authenticate-with-a-personal-access-token-in-maven)
  in order to download comm-centre releases.
- Set the environment variable `GITLAB_KEY`.

- Install and configure Git LFS.

```
brew install git-lfs

## To track a file
git lfs install #at project root folder
git lfs track "*.pdf" 
git lfs track "*.png" 
git lfs track "*.jpeg"

## To fetch files
git lfs pull 
```

## Criar um módulo

- É altamente recomendável que o nome do módulo esteja presente no pacote raiz do módulo. Uma vez que o build junta tudo em um jar "Flat", sem módulos e pode haver clash de nomes.

## Pré commit hook

- Para formatar o código automaticamente, voce pode instalar o pré commit hook com o comando `make pre-comit-install`.

## Melhorias pendentes

- Testes hexagonais não estão sendo executados nos submodulos.
- Pode ser necessário mover mais classes para o pacote testFixtures. Foram movidos apenas os necessários atualmente.
- Ao testar pela IDE, verifique que os testes dos módulos foram realizados (basta procurar por algum teste ao final)

## Testing locally

Build the service.
> ./gradlew clean build

## Deploy Staging

Para realizar um deploy em stagin basta ter no commit uma hashtag `#deploystg` (Friday)