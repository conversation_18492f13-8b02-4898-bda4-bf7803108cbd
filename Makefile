	DEFAULT_GOAL = compile

GRADLEW = ./gradlew

.PHONY: compile clean format-cc cc lint format

package:
	$(GRA<PERSON>LEW) build testClasses -x test

clean:
	$(GRADLEW) clean

format-cc: format cc

cc:
	$(GRADLEW) clean build testClasses -x test

lint:
	$(GRADLEW) ktlintCheck

format:
	$(GRADLEW) ktlintFormat

pre-commit-install:
	$(GRADLEW) addKtlintFormatGitPreCommitHook

motorola-deploy-stg:
	export AWS_REGION="us-east-1" && cd ci && REGION="us-east-1" TENANT=motorola AWS_ACCOUNT_ID=************ ./deploy_ecs.sh

motorola-deploy-prod:
	export AWS_REGION="us-east-1" && cd ci && REGION="us-east-1" TENANT=motorola AWS_ACCOUNT_ID=************ ./deploy_ecs.sh

gigu-deploy-prod:
	export AWS_REGION="us-east-1" && cd ci && REGION="us-east-1" TENANT=gigu AWS_ACCOUNT_ID=************ ./deploy_ecs.sh

motorola-run:
	LOGGER_LEVELS_ROOT=INFO MICRONAUT_ENVIRONMENTS=friday,motorola,prodmotorola ./gradlew application:run

run-friday:
	LOGGER_LEVELS_ROOT=INFO MICRONAUT_ENVIRONMENTS=friday,staging ./gradlew application:run

run-me-poupe:
	LOGGER_LEVELS_ROOT=INFO MICRONAUT_ENVIRONMENTS=me-poupe,stgme-poupe ./gradlew application:run

run-modatta:
	LOGGER_LEVELS_ROOT=INFO MICRONAUT_ENVIRONMENTS=modatta,stgmodatta ./gradlew application:run