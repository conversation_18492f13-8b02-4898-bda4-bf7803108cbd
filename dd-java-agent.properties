dd.trace.annotations=io.micronaut.scheduling.annotation.Scheduled
dd.trace.methods=ai.friday.billpayment.adapters.messaging.SQSBankAccountDepositHandler[tryProcessMessage];ai.friday.billpayment.adapters.messaging.SQSBillEventNotificationHandler[tryProcessMessage];ai.friday.billpayment.adapters.messaging.SQSBillReceiptGeneratorHandler[tryProcessMessage];ai.friday.billpayment.adapters.messaging.SQSValidateBoletoJaBaixadoHandler[tryProcessMessage];ai.friday.billpayment.adapters.messaging.SQSSchedulingHandler[tryProcessMessage];io.via1.communicationcentre.app.integrations.NotificationService[notify];ai.friday.billpayment.app.integrations.MessageProcessor[process]



