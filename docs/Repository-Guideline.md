# Guia de Criação de Repository (DynamoDB – Projeto bill-payment-service)

Este guia descreve o padrão de implementação de repositories utilizado no projeto, com base nas classes existentes. Abrange:
- Estrutura de camadas (Interface de domínio, DAO, Entity, Repository)
- Anotações e mapeamentos do DynamoDB Enhanced Client
- Uso de Índices Secundários Globais (GSIs)
- Conversores de atributos (AttributeConverter)
- Contador atômico (padrões recomendados)
- Boas práticas e testes

Referências no código:
- Base DAO: `adapters/dynamodb/AbstractDynamoDAO.kt`, `adapters/dynamodb/AbstractTables.kt`
- Exemplo completo: `adapters/dynamodb/AccountRegisterDbRepository.kt`
- Conversores: `adapters/converters/DynamoDbFieldConverter.kt`, `adapters/converters/CustomS3LinkConverter.kt`

## 1) Padrão arquitetural

1. Interface de domínio (contrato): definida no módulo `app`, arquivo `app/integrations/Interfaces.kt`. Ex.: `AccountRegisterRepository`, `BillRepository` etc.
2. Entity (persistência): classe anotada com `@DynamoDbBean`, mapeando colunas/atributos e chaves para a tabela do DynamoDB.
3. DAO (acesso à tabela): classe que estende `AbstractBillPaymentDynamoDAO<T>` (ou `AbstractBillPaymentDynamoDAOAsync<T>`), provendo operações de CRUD e de consulta em índices via AWS Enhanced Client.
4. Repository (adapter): classe `@Singleton` que implementa a interface de domínio, injeta o DAO e faz a tradução Entity ↔ Domínio, além de regras de consulta e validação.


## 2) Passo a passo para criar um novo Repository

### 2.1 Defina a interface no domínio
Crie ou edite o contrato em `app/integrations/Interfaces.kt`:

```kotlin
interface MinhaCoisaRepository {
    fun findById(id: MinhaCoisaId): MinhaCoisa
    fun save(model: MinhaCoisa): MinhaCoisa
}
```

### 2.2 Modele a Entity com anotações DynamoDB
Crie uma classe `@DynamoDbBean` no pacote `adapters/dynamodb` (ou no módulo específico), seguindo o padrão de chaves e atributos.

Padrões utilizados no projeto:
- `@DynamoDbPartitionKey` + `@DynamoDbAttribute(BILL_PAYMENT_PARTITION_KEY)` → chave de partição (ex.: `primaryKey`)
- `@DynamoDbSortKey` + `@DynamoDbAttribute(BILL_PAYMENT_RANGE_KEY)` → chave de ordenação (ex.: `scanKey`)
- GSIs: `@DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])` e `@DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])` (mesmo para `GSIndex2` etc.)
- Conversores: `@DynamoDbConvertedBy(CustomXAttributeConverter::class)` em campos que precisam (JSON, tipos complexos, etc.)

Exemplo mínimo:
```kotlin
@DynamoDbBean
class MinhaCoisaEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbAttribute("Nome")
    var nome: String? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute("GSIndex1PrimaryKey")
    var gsi1Pk: String? = null

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute("GSIndex1ScanKey")
    var gsi1Sk: String? = null
}
```

Chaves e índices: use nomes coerentes com o padrão global do projeto (`BILL_PAYMENT_PARTITION_KEY`, `BILL_PAYMENT_RANGE_KEY`, `GSIndex1`, `GSIndex2`).

### 2.3 Crie o DAO
Crie uma classe que estende `AbstractBillPaymentDynamoDAO<MinhaCoisaEntity>` e injete o `DynamoDbEnhancedClient` via construtor:

```kotlin
@jakarta.inject.Singleton
class MinhaCoisaDynamoDAO(cli: DynamoDbEnhancedClient) :
    AbstractBillPaymentDynamoDAO<MinhaCoisaEntity>(cli, MinhaCoisaEntity::class.java)
```

Com isso, você herda:
- `save(item)`, `update(item, ignoreNulls)`, `delete(pk, sk)`
- Métodos de consulta por GSI e por chaves, ex.: `findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex1, pk)`

### 2.4 Implemente o Repository (adapter)
Implemente a interface do domínio, injete o DAO e faça os mapeamentos.

```kotlin
@Singleton
class MinhaCoisaDbRepository(
    private val dao: MinhaCoisaDynamoDAO,
) : MinhaCoisaRepository {

    override fun findById(id: MinhaCoisaId): MinhaCoisa =
        dao.findByPrimaryKey(partitionKeyFrom(id), sortKeyFrom(id))
            ?.toDomain()
            ?: throw ItemNotFoundException("MinhaCoisa não encontrada: $id")

    override fun save(model: MinhaCoisa): MinhaCoisa {
        val entity = model.toEntity()
        dao.save(entity)
        return entity.toDomain()
    }
}
```

Mapeamentos: modele `toEntity()` e `toDomain()` próximos ao repository (ou em arquivos utilitários), como visto em `AccountRegisterDbRepository.kt`.


## 3) Conversores (AttributeConverter)
Quando um campo não é primitivo (ou precisa de serialização específica), crie um conversor e anote o campo com `@DynamoDbConvertedBy`.

Padrão (Enhanced Client):
```kotlin
class CustomS3LinkAttributeConverter : AttributeConverter<CustomS3Link> {
    private val objectMapper = ObjectMapper()

    override fun transformFrom(value: CustomS3Link): AttributeValue =
        AttributeValue.builder().s(objectMapper.writeValueAsString(mapOf(
            "s3" to mapOf(
                "bucket" to value.bucket,
                "key" to value.key,
                "region" to value.region,
            )
        ))).build()

    override fun transformTo(attributeValue: AttributeValue?): CustomS3Link {
        val data = attributeValue?.s() ?: error("Valor nulo")
        val root = objectMapper.readTree(data).get("s3") ?: error("nó 's3' ausente")
        return CustomS3Link(
            bucket = root.get("bucket").asText(),
            key = root.get("key").asText(),
            region = root.get("region")?.asText() ?: "us-east-1",
        )
    }

    override fun type() = EnhancedType.of(CustomS3Link::class.java)
    override fun attributeValueType() = AttributeValueType.S
}
```
Uso na Entity:
```kotlin
@get:DynamoDbConvertedBy(CustomS3LinkAttributeConverter::class)
@get:DynamoDbAttribute("KycFile")
var kycFile: CustomS3Link? = null
```
Exemplos no projeto:
- `adapters/converters/DynamoDbFieldConverter.kt` (inclui `CustomS3LinkAttributeConverter` e outros)
- `AccountRegisterEntity` usando `@DynamoDbConvertedBy`


## 4) Contador atômico
Existem três abordagens a considerar:

1) Simples (usando Enhanced Client update)
- O método `update(item, ignoreNulls)` do `AbstractDynamoDAO` faz um `UpdateItem` atômico no item. Você pode atualizar apenas o campo do contador (com `ignoreNulls = true` para não sobrescrever outros campos nulos). Essa abordagem é suficiente quando não há concorrência alta de incrementos simultâneos e você controla o novo valor no lado da aplicação.
- Observação: Não garante incremento “ADD” nativo, portanto, sob concorrência intensa, pode ocorrer last-writer-wins. Use quando esse risco é aceitável.

2) Robusta (recomendada) — incrementos com expressão `ADD`
- Para um contador verdadeiramente atômico sob concorrência, utilize a operação `UpdateItem` com expressão `ADD` do DynamoDB (API de baixo nível). Exemplo (Kotlin):

```kotlin
fun incrementCounter(
    ddb: DynamoDbClient,
    table: String,
    pkName: String,
    skName: String,
    pk: String,
    sk: String,
    attribute: String,
    delta: Long,
) {
    val key = mapOf(
        pkName to AttributeValue.builder().s(pk).build(),
        skName to AttributeValue.builder().s(sk).build(),
    )
    val request = UpdateItemRequest.builder()
        .tableName(table)
        .key(key)
        .updateExpression("ADD #attr :inc")
        .expressionAttributeNames(mapOf("#attr" to attribute))
        .expressionAttributeValues(mapOf(":inc" to AttributeValue.builder().n(delta.toString()).build()))
        .build()
    ddb.updateItem(request)
}
```
- Dicas:
  - Se o atributo ainda não existir, `ADD` cria e inicializa com o incremento.
  - Combine com `ConditionExpression` caso deseje invariantes adicionais.

Em ambos os casos, o campo do contador na Entity deve ser do tipo numérico (`Long?`/`Int?`) e anotado com `@DynamoDbAttribute("MeuContador")`.

### 3) Por anotação (@DynamoDbAtomicCounter) — usada em LimitEntity
Essa é a abordagem utilizada no repository de limite. Você anota o campo contador na Entity com `@DynamoDbAtomicCounter`, definindo `startValue` e `delta`. O incremento acontece quando você chama `update` no DAO para o item carregado.

Exemplo real (trechos simplificados):

Entity (src/main/kotlin/ai/friday/billpayment/adapters/dynamodb/LimitDbRepository.kt):
```kotlin
@DynamoDbBean
class LimitEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String // ACCOUNT_ID

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // PREFIX#KEY

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "ExpirationTTL")
    var expirationTtl: Long = 0

    @get:DynamoDbAtomicCounter(startValue = 0, delta = 1)
    @get:DynamoDbAttribute(value = "Count")
    var count: Int = 0

    @get:DynamoDbAttribute(value = "MaxCount")
    var maxCount: Int = 0
}
```

Repository increment (carrega e dá update para disparar o contador):
```kotlin
override fun increment(accountId: AccountId, key: String) {
    client.findByPrimaryKey(accountId.value, generateScan(key))
        ?.let(client::update) // incrementa o campo anotado com @DynamoDbAtomicCounter
}
```

Configuração do Enhanced Client (habilitar a extensão de contador atômico):
```kotlin
val enhanced = DynamoDbEnhancedClient.builder()
    .dynamoDbClient(dynamoDbClient)
    .extensions(
        software.amazon.awssdk.enhanced.dynamodb.extensions.AtomicCounterExtension.builder().build()
    )
    .build()
```

Observações:
- Use `update(item)` (e não `save/put`) para que a extensão processe o incremento no campo anotado.
- `delta` define quanto incrementa por atualização; `startValue` é o valor inicial quando o item é criado.
- TTL (ExpirationTTL) pode ser usado para expirar a chave de limite como no exemplo acima.


## 5) Padrões de consulta e índices
- Use os helpers herdados do DAO:
  - `findByPrimaryKey(pk, sk)`
  - `findByPartitionKey(pk)`
  - `findBeginsWithOnSortKey(pk, prefix)`
  - Em GSI: `findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex1, gsiPk)` e similares.
- Defina PK/SK e GSIs com prefixos bem definidos (ex.: `ACCOUNT-REGISTER#<doc>`), como visto em `AccountRegisterDbRepository.kt`.

### 5.1) Expressions para consultas personalizadas (FilterExpression)
Quando precisar filtrar além da condição de chave do índice (PK + `begins_with` no SK), utilize os parâmetros `expression`, `expressionNames` e `expressionValues` disponíveis nos métodos do DAO (por exemplo: `findBeginsWithOnIndex`).

Observações importantes:
- O KeyCondition (PK + `begins_with` do SK) é montado pelo DAO. A `expression` fornecida é aplicada como FilterExpression (pós-chave), reduzindo os resultados retornados pelo índice.
- Sempre use placeholders:
  - Para nomes de atributos: `#nome` (evita conflito com palavras reservadas). Mapeie em `expressionNames`.
  - Para valores: `:valor`. Mapeie em `expressionValues` como `AttributeValue` com o tipo correto (`s`, `n`, `bool`, etc.).
- Dicas práticas:
  - `begins_with` também pode ser usado em atributos não-chave de texto (ex.: `Created`), útil para prefixos de data ("2025-08").
  - Combine operadores lógicos (`and`/`or`) para compor filtros mais complexos.
  - Garanta coerência de tipos ao construir `AttributeValue` (String vs Number).

Exemplo (baseado em `DynamoDbTransactionRepository.findCreditCardUsage`):
```kotlin
val items = transactionDAO.findBeginsWithOnIndex(
    index = GlobalSecondaryIndexNames.GSIndex1,
    partitionKey = walletId.value,
    sortKey = "TRANSACTION",
    expression = "(#status = :v1 or #status = :v2) and begins_with(#created, :v3) and #pmType = :v4 and #payer = :v5",
    expressionNames = mapOf(
        "#status" to "Status",
        "#created" to "Created",
        "#pmType" to "PaymentMethodType",
        "#payer" to "PayerAccountId",
    ),
    expressionValues = mapOf(
        ":v1" to AttributeValue.builder().s(TransactionStatus.COMPLETED.name).build(),
        ":v2" to AttributeValue.builder().s(TransactionStatus.PROCESSING.name).build(),
        ":v3" to AttributeValue.builder().s(datePattern).build(),   // ex.: "2025-08"
        ":v4" to AttributeValue.builder().s("CREDIT_CARD").build(),
        ":v5" to AttributeValue.builder().s(accountId.value).build(),
    ),
)
```
Veja também:
- `src/main/kotlin/ai/friday/billpayment/adapters/dynamodb/DynamoDbTransactionRepository.kt` (método `findCreditCardUsage`).
- `adapters/dynamodb/AbstractDynamoDAO.kt` (assinatura de `findBeginsWithOnIndex` com `expression`).


## 6) Boas práticas
- Convenções de nomes:
  - Entities terminam com `Entity`
  - DAOs terminam com `DynamoDAO`
  - Repositories terminam com `DbRepository`
- Datas/Horários: padronize formatação string (ex.: utilitários `dateFormat`/`dateTimeFormat`, `BrazilZonedDateTimeSupplier.getZonedDateTime()`), como nos repositories existentes.
- Mapeamento claro Entity ↔ Domínio e vice‑versa (isolado no adapter, evitando vazar tipos de persistência para o domínio).
- Tratamento de erros com exceções de integração (ex.: `ItemNotFoundException`).
- Prefira consultas por índice em vez de scan.
- Para campos complexos, use `@DynamoDbConvertedBy` com um `AttributeConverter` dedicado.


## 7) Testes
- Crie testes de repositório em `src/test/kotlin/...` (há diversos exemplos no projeto, inclusive nos módulos). 
- Recomendações:
  - Teste operações básicas (save, update, find/delete)
  - Teste consultas em GSI
  - Teste conversores (há exemplo: `CustomS3LinkConverterTest.kt`)
  - Teste o comportamento do contador atômico (quando aplicável)


## 8) Checklist rápido
- [ ] Interface em `app/integrations/Interfaces.kt`
- [ ] Entity com `@DynamoDbBean` e chaves/GSIs
- [ ] Conversores necessários com `@DynamoDbConvertedBy`
- [ ] DAO extendendo `AbstractBillPaymentDynamoDAO`
- [ ] Repository `@Singleton` implementando o contrato
- [ ] Consultas usando métodos de DAO (incluindo GSIs)
- [ ] Testes cobrindo CRUD, índices, conversores e contador (se houver)

---

Para exemplos reais, veja:
- `src/main/kotlin/ai/friday/billpayment/adapters/dynamodb/AccountRegisterDbRepository.kt`
- `src/main/kotlin/ai/friday/billpayment/adapters/dynamodb/DynamoDBBillRepository.kt`
- `src/main/kotlin/ai/friday/billpayment/adapters/dynamodb/WalletDbRepository.kt`
- `src/main/kotlin/ai/friday/billpayment/adapters/converters/DynamoDbFieldConverter.kt`
